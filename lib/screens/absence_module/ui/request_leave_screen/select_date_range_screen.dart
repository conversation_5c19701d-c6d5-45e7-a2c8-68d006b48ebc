import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_loader.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/bloc/request_leave_screen_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

class SelectDateRangeScreen extends StatefulWidget {
  @override
  State<SelectDateRangeScreen> createState() => _SelectDateRangeScreenState();
}

class _SelectDateRangeScreenState extends State<SelectDateRangeScreen> {
  DateTime? _minDate;
  PickerDateRange? selectedRange;
  String startDate = "";
  String endDate = "";
  bool okButtonOn = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _calculateMinDate();
  }

  void _calculateMinDate() {
    DateTime now = DateTime.now();
    _minDate = DateTime(now.year, now.month, 1);
  }

  @override
  Widget build(BuildContext context) {
    final requestLeaveBloc = BlocProvider.of<RequestLeaveCubit>(context, listen: false);

    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      body: Center(
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(top: AppSize.h30),
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () {
                      AppNavigation.previousScreen(context);
                    },
                    child: Text(AppLocalizations.of(context)!.cancelText.toUpperCase(),
                        style:
                            context.textTheme.bodyLarge!.copyWith(color: AppColors.white, fontWeight: FontWeight.w500)),
                  ),
                  Text(AppLocalizations.of(context)!.selectPeriodText,
                      style: context.textTheme.headlineLarge!
                          .copyWith(color: AppColors.white, fontSize: AppSize.sp17, fontWeight: FontWeight.w500)),
                  TextButton(
                    onPressed: startDate != "" || endDate != ""
                        ? () async {
                            Loader.showLoaderDialog(context);
                            startDate = DateFormatFunctions.formatDate(selectedRange?.startDate ?? DateTime.now());
                            endDate = DateFormatFunctions.formatDate(selectedRange?.endDate ?? DateTime.now());
                            print('startDate ---->$startDate');
                            print('endDate ---->$endDate');
                            requestLeaveBloc.setDateRange(
                                "${DateFormatFunctions.formatDay(selectedRange?.startDate?.day ?? 00)}/${DateFormatFunctions.formatDay(selectedRange?.startDate?.month ?? 00)}/${selectedRange?.startDate?.year} t/m ${DateFormatFunctions.formatDay(selectedRange?.endDate?.day ?? 00)}/${DateFormatFunctions.formatDay(selectedRange?.endDate?.month ?? 00)}/${selectedRange?.endDate?.year}");
                            requestLeaveBloc.selectedRange(context: context, selectedRange: selectedRange);

                            await requestLeaveBloc.myHoursInPeriodApiCall(
                                context: context, startDate: "$startDate", endDate: "$endDate");
                            Loader.closeLoadingDialog(context);

                            AppNavigation.previousScreen(context);
                          }
                        : null,
                    child: Text(AppLocalizations.of(context)!.oK,
                        style: context.textTheme.bodyLarge!.copyWith(
                            color: !(selectedRange?.startDate?.day == null) && !(selectedRange?.endDate?.day == null)
                                ? AppColors.white
                                : AppColors.white.withOpacity(0.6),
                            fontWeight: FontWeight.w500)),
                  ),
                ],
              ),
              // height: AppSize.h14,
            ),
            Expanded(
              child: SfDateRangePicker(
                selectionMode: DateRangePickerSelectionMode.range,
                onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
                  if (args.value != null) {
                    print("object: ${args.value}");
                    selectedRange = args.value;

                    startDate = DateFormatFunctions.formatDate(selectedRange?.startDate ?? DateTime.now());
                    endDate = DateFormatFunctions.formatDate(selectedRange?.endDate ?? DateTime.now());

                    // startDate =
                    //     "${selectedRange?.startDate?.year}${DateFormatFunctions.formatDay(selectedRange?.startDate?.month ?? 00)}${DateFormatFunctions.formatDay(selectedRange?.startDate?.day ?? 00)}";
                    // endDate =
                    //     "${selectedRange?.endDate?.year}${DateFormatFunctions.formatDay(selectedRange?.endDate?.month ?? 00)}${DateFormatFunctions.formatDay(selectedRange?.endDate?.day ?? 00)}";
                    print(startDate);
                    print(endDate);
                  }
                  setState(() {});
                },
                navigationDirection: DateRangePickerNavigationDirection.vertical,
                navigationMode: DateRangePickerNavigationMode.scroll,
                enablePastDates: false,
                view: DateRangePickerView.month,
                enableMultiView: true,
                startRangeSelectionColor: AppColors.primaryColor,
                endRangeSelectionColor: AppColors.primaryColor,
                monthFormat: "MMM.",
                monthViewSettings: DateRangePickerMonthViewSettings(
                  firstDayOfWeek: 1,
                  viewHeaderStyle: DateRangePickerViewHeaderStyle(
                      backgroundColor: AppColors.primaryColor,
                      textStyle: TextStyle(
                        color: AppColors.white,
                        fontSize: AppSize.sp13,
                      )),
                  viewHeaderHeight: AppSize.h50,
                ),
                //initialSelectedRange: requestLeaveBloc.selectedRanges.value,
                rangeSelectionColor: AppColors.primaryColor.withOpacity(0.75),
                rangeTextStyle: TextStyle(color: AppColors.white, fontSize: AppSize.sp16),
                headerStyle: DateRangePickerHeaderStyle(
                    textAlign: TextAlign.center,
                    backgroundColor: context.themeColors.homeContainerColor,
                    textStyle: TextStyle(color: context.themeColors.textColor)),
                monthCellStyle: DateRangePickerMonthCellStyle(
                  todayCellDecoration: BoxDecoration(border: null),
                  textStyle: TextStyle(fontSize: AppSize.sp16, color: context.themeColors.textColor),
                  todayTextStyle:
                      TextStyle(color: AppColors.primaryColor, fontWeight: FontWeight.bold, fontSize: AppSize.sp16),
                  disabledDatesTextStyle:
                      TextStyle(color: AppColors.greyColor.withOpacity(0.5), fontSize: AppSize.sp16),
                ),
                selectionTextStyle: TextStyle(color: AppColors.white, fontSize: AppSize.sp16),
                selectionRadius: 20,
                minDate: _minDate,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
