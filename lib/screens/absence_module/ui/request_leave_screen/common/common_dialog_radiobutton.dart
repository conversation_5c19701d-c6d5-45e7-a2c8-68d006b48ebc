import 'package:flutter/material.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

class CustomRadioButtonDialog extends StatefulWidget {
  final String dialogTitle;
  final List<String> list;
  final Function(String) onOKPressed;
  final String selectedValue;

  CustomRadioButtonDialog({
    required this.dialogTitle,
    required this.list,
    required this.onOKPressed,
    required this.selectedValue,
  });

  @override
  _CustomRadioButtonDialogState createState() =>
      _CustomRadioButtonDialogState(selectedValue);
}

class _CustomRadioButtonDialogState extends State<CustomRadioButtonDialog> {
  String _selectedValue;

  _CustomRadioButtonDialogState(this._selectedValue);

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: context.themeColors.cardColor,
      actionsPadding: EdgeInsets.all(0),
      titlePadding: EdgeInsets.only(
        top: AppSize.sp16,
        left: AppSize.sp16,
        bottom: AppSize.sp10,
      ),
      contentPadding: EdgeInsets.all(0),
      title: Text(
        widget.dialogTitle,
        style: context.textTheme.headlineLarge?.copyWith(
          fontSize: AppSize.sp18,
          fontWeight: FontWeight.w500,
          color: context.themeColors.textColor,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Divider(
            thickness: 1,
          ),
          Padding(
            padding: EdgeInsets.only(left: AppSize.sp8),
            child: StatefulBuilder(builder: (ctx, setState2) {
              return Column(
                children: widget.list.map((language) {
                  return Transform.scale(
                    scale: 1.1,
                    child: RadioListTile<String>(
                      activeColor: context.themeColors.primaryColor,
                      dense: true,
                      title: Text(
                        language,
                        style: context.textTheme.headlineLarge?.copyWith(
                          fontSize: AppSize.sp14,
                          fontWeight: FontWeight.normal,
                          color: context.themeColors.textColor,
                        ),
                      ),
                      value: language,
                      groupValue: _selectedValue,
                      onChanged: (value) {
                        setState2(() {
                          _selectedValue = value!;
                        });
                      },
                    ),
                  );
                }).toList(),
              );
            }),
          ),
          Divider(
            thickness: 1,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: Text(
            AppLocalizations.of(context)!.cancelText,
            style: context.textTheme.headlineLarge?.copyWith(
              fontSize: AppSize.sp14,
              fontWeight: FontWeight.normal,
              color: context.themeColors.primaryColor,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            widget.onOKPressed(_selectedValue);
            Navigator.pop(context);
          },
          child: Text(
            "OK",
            style: context.textTheme.headlineLarge?.copyWith(
              fontSize: AppSize.sp14,
              fontWeight: FontWeight.normal,
              color: context.themeColors.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}

