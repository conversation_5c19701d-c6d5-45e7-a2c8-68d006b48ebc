// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:intl/intl.dart';
// import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
// import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
// import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
// import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/day_widget.dart';
// import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/month_widget.dart';
// import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/year_widget.dart';
// import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
// import 'package:staff_medewerker/utils/appsize.dart';
// import 'package:staff_medewerker/l10n/app_localizations.dart';

// class DatePickerWidget extends StatelessWidget {
//   final Function(DateTime) onOkPressed;

//   DatePickerWidget({required this.onOkPressed});

//   @override
//   Widget build(BuildContext context) {
//     DateFormat format = DateFormat("MMM dd, yyyy");
//     return BlocProvider(
//       create: (ctx) => DateCubit()..setDate(format.parse(BlocProvider.of<AbsenceCubit>(context).selectedDateString.value)),
//       child: Container(
//         height: MediaQuery.of(context).size.height * .334,
//         child: Column(
//           children: [
//             Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 TextButton(
//                   onPressed: () {
//                     AppNavigation.previousScreen(context);
//                   },
//                   child: Text(AppLocalizations.of(context)!.cancelText.toUpperCase()),
//                 ),
//                 BlocBuilder<DateCubit, DateTime>(
//                   builder: (context, state) {
//                     return TextButton(
//                       onPressed: () {
//                         onOkPressed(state);
//                         Navigator.pop(context);
//                       },
//                       child: Text(AppLocalizations.of(context)!.oK),
//                     );
//                   },
//                 ),
//               ],
//             ),
//             Expanded(
//               child: Row(
//                 children: [
//                   SpaceH(AppSize.w70),
//                   Expanded(
//                     child: AbsenceMonthPicker(),
//                   ),
//                   Expanded(
//                     child: AbsenceDayPicker(),
//                   ),
//                   Expanded(
//                     child: AbsenceYearPicker(),
//                   ),
//                   SpaceH(AppSize.w70),
//                 ],
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }


