import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

class AbsenceMonthPicker extends StatelessWidget {
  final List<String> monthNames = [
    AppLocalizations.of(navigatorKey.currentContext!)!.january,
    AppLocalizations.of(navigatorKey.currentContext!)!.february,
    AppLocalizations.of(navigatorKey.currentContext!)!.march,
    AppLocalizations.of(navigatorKey.currentContext!)!.april,
    AppLocalizations.of(navigatorKey.currentContext!)!.may,
    AppLocalizations.of(navigatorKey.currentContext!)!.june,
    AppLocalizations.of(navigatorKey.currentContext!)!.july,
    AppLocalizations.of(navigatorKey.currentContext!)!.august,
    AppLocalizations.of(navigatorKey.currentContext!)!.september,
    AppLocalizations.of(navigatorKey.currentContext!)!.october,
    AppLocalizations.of(navigatorKey.currentContext!)!.november,
    AppLocalizations.of(navigatorKey.currentContext!)!.december

    // 'January',
    // 'February',
    // 'March',
    // 'April',
    // 'May',
    // 'June',
    // 'July',
    // 'August',
    // 'September',
    // 'October',
    // 'November',
    // 'December'
  ];

  @override
  Widget build(BuildContext context) {
    DateFormat format = DateFormat("MMM dd, yyyy");

    int currentMonth = format.parse(BlocProvider.of<AbsenceCubit>(context).selectedDateString.value).month;
    return BlocBuilder<DateCubit, DateTime>(
      builder: (context, date) {
        return ListWheelScrollView(
          itemExtent: 50,
          perspective: 0.**********,
          useMagnifier: false,
          physics: FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            context.read<DateCubit>().setMonth(index + 1);
          },
          children: List<Widget>.generate(12, (index) {
            final value = index + 1;
            final isSelected = value == date.month;
            return Text(monthNames[index].substring(0, 3), // Use month names
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: isSelected ? AppSize.sp20 : AppSize.sp16,
                  color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
                ));
          }),
          controller: FixedExtentScrollController(initialItem: currentMonth - 1),
        );
      },
    );
  }
}
