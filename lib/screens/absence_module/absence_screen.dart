import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/bloc/absence_screen_cubit.dart';
import 'package:staff_medewerker/screens/absence_module/ui/my_request_screen/leave_request_screen.dart';
import 'package:staff_medewerker/screens/absence_module/ui/request_leave_screen/request_leave_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class AbsenceScreen extends StatefulWidget {
  const AbsenceScreen({Key? key}) : super(key: key);

  @override
  State<AbsenceScreen> createState() => _AbsenceScreenState();
}

class _AbsenceScreenState extends State<AbsenceScreen> {
  final absenceBloc =
      BlocProvider.of<AbsenceCubit>(navigatorKey.currentContext!);

  @override
  void initState() {
    super.initState();
    // Load the requests data when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      absenceBloc.timesheetLeaveInYearApiCall(
          context: context, selectedYear: '${DateTime.now().year}');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.absence),
      body: RefreshIndicator(
        onRefresh: () {
          return absenceBloc.timesheetLeaveInYearApiCall(
              context: context, selectedYear: '${DateTime.now().year}');
        },
        child: Column(
          children: [
            // Requests List
            Expanded(
              child: ValueListenableBuilder(
                valueListenable: absenceBloc.isLoading,
                builder: (BuildContext context, isLoading, Widget? child) {
                  if (!isLoading &&
                      absenceBloc.timesheetLeaveInYearList.isEmpty) {
                    return Center(
                      child: Text(AppLocalizations.of(context)!.noDataFound,
                          style: context.textTheme.bodyMedium?.copyWith(
                              fontSize: AppSize.sp15,
                              color: context.themeColors.textColor)),
                    );
                  } else if (!isLoading &&
                      absenceBloc.timesheetLeaveInYearList.isNotEmpty) {
                    return ListView.builder(
                      itemCount: absenceBloc.timesheetLeaveInYearList.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          title: Text(
                              absenceBloc.timesheetLeaveInYearList[index]
                                      .soortVerlof ??
                                  '',
                              style: context.textTheme.bodyMedium?.copyWith(
                                  color: context.themeColors.textColor,
                                  fontSize: AppSize.sp15)),
                          subtitle: Text(
                              "${formatDate(absenceBloc.timesheetLeaveInYearList[index].begin ?? DateTime.now())} - ${formatDate(absenceBloc.timesheetLeaveInYearList[index].einde ?? DateTime.now())}",
                              style: context.textTheme.titleMedium?.copyWith(
                                  fontSize: AppSize.sp13,
                                  color: context.themeColors.darkGreyColor)),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                absenceBloc.timesheetLeaveInYearList[index]
                                        .toestand ??
                                    '',
                                style: context.textTheme.bodySmall?.copyWith(
                                    color: context.themeColors.darkGreyColor),
                              ),
                              SpaceH(AppSize.w4),
                              Icon(
                                Ionicons.chevron_forward_outline,
                                color: context.themeColors.greyColor,
                                size: AppSize.sp18,
                              )
                            ],
                          ),
                          onTap: () {
                            // Navigate to request details - using the same logic as MyRequestScreen
                            final formatter =
                                DateFormat('MMM dd, yyy', appDB.language);
                            AppNavigation.nextScreen(
                                context,
                                LeaveRequestScreen(
                                    startDate: formatter.format(absenceBloc
                                            .timesheetLeaveInYearList[index]
                                            .begin ??
                                        DateTime.now()),
                                    endDate: formatter.format(absenceBloc
                                            .timesheetLeaveInYearList[index]
                                            .einde ??
                                        DateTime.now()),
                                    submittedOn: formatter.format(absenceBloc
                                            .timesheetLeaveInYearList[index]
                                            .invoerdatum ??
                                        DateTime.now()),
                                    leaveType: absenceBloc
                                            .timesheetLeaveInYearList[index]
                                            .soortVerlof ??
                                        '',
                                    state: absenceBloc.timesheetLeaveInYearList[index].toestand ?? '',
                                    requestedHours: absenceBloc.timesheetLeaveInYearList[index].urenAangevraagd.toString(),
                                    registeredHours: absenceBloc.timesheetLeaveInYearList[index].urenGeregistreerd.toString(),
                                    reason: absenceBloc.timesheetLeaveInYearList[index].reden ?? ''));
                          },
                        );
                      },
                    );
                  } else {
                    return Center(
                      child: Image.asset(
                        'assets/gif/loader_gif.gif',
                        height: AppSize.h60,
                        width: AppSize.w60,
                        color: context.themeColors.textColor,
                      ),
                    );
                  }
                },
              ),
            ),
            // Create New Request Button
            Container(
              width: double.infinity,
              margin: EdgeInsets.all(AppSize.w12),
              child: ElevatedButton(
                onPressed: () {
                  AppNavigation.nextScreen(context, RequestLeaveScreen());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: AppSize.h12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(AppSize.w8),
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context)!.createNewRequestText,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: AppColors.white,
                    fontSize: AppSize.sp16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String formatDate(DateTime date) {
    final formatter = DateFormat('E dd MMM', appDB.language);
    return formatter.format(date);
  }
}
