import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/app/db/app_db_models/user_credential.dart';
import 'package:staff_medewerker/common/functions/shared_prefs.dart';
import 'package:staff_medewerker/screens/home_module/ui/bottom_bar_screen.dart';
import 'package:staff_medewerker/screens/pin_module/common_show_dialog.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../../app/db/app_db_models/user_data.dart';
import '../../../../common/custom_widgets/common_snackbar.dart';
import '../../../../common/custom_widgets/custom_loader.dart';
import '../../../../main.dart';
import '../login_repository/login_api_repository.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit() : super(LoginInitial());

  Future<void> loginApi(
    BuildContext context,
    String username,
    String password,
    String deviceId,
    bool isFromPinScreen,
    bool isFromScannerScreen,
  ) async {
    if (username.isEmpty && password.isEmpty) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.enterUserNamePassword,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    } else {
      Loader.showLoaderDialog(navigatorKey.currentContext!);
      FocusManager.instance.primaryFocus?.unfocus();

      final LoginApiRepository loginApiRepository = LoginApiRepository();
      if (username.isNotEmpty || password.isNotEmpty) {
        final response = await loginApiRepository.loginApiData(
            context: context, username: username, password: password, deviceId: deviceId);
        print("response statusCode =====>${response?.statusCode}");
        if (response?.statusCode == 200) {
          Loader.closeLoadingDialog(navigatorKey.currentContext!);
          // customSnackBar(
          //   context: navigatorKey.currentContext!,
          //   message: 'Verification Done',
          //   actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!
          //       .closeText.toUpperCase(),
          // );
          if (isFromPinScreen) {
            // this if condition if you reset local set pin login
            Prefs.preferences.remove(AppConstants.savedPinNumber);
            showDialog(
              barrierDismissible: false,
              context: context,
              builder: (context) {
                return CustomAlertDialog(
                  context: context,
                  title: AppLocalizations.of(context)!.pinResetText,
                  message: AppLocalizations.of(context)!.pinResetSuccessText,
                  onOkPressed: () {
                    Prefs.preferences.remove(AppConstants.savedPinNumber);
                    appDB.savedPinNumber = '';
                    AppNavigation.nextScreen(
                        context,
                        BottomBarScreen(
                          isApiCall: true,
                        ));
                  },
                );
              },
            );
          } else {
            UserData data = UserData(
              personId: response?.personId ?? '',
              APIKey: response?.APIKey,
              AppFunctionRightsList: response?.AppFunctionRights,
              Culture: response?.Culture,
              DefaultSkinChanged: response?.DefaultSkinChanged,
              DefaultSkinId: response?.DefaultSkinId,
              DefaultSkinPath: response?.DefaultSkinPath,
              InitialsPerson: response?.InitialsPerson,
              IPAddressesSpecificUseList: response?.IPAddressesSpecificUse,
              Language: response?.Language,
              LanguageId: response?.LanguageId,
              LicenseId: response?.LicenseId,
              LicenseName: response?.LicenseName,
              LicenseSupportMessage: response?.LicenseSupportMessage,
              LicenseSupportMessageMobile: response?.LicenseSupportMessageMobile,
              MustChangePassword: response?.MustChangePassword,
              MustEnterTwoFactorAuthenticationToken: response?.MustEnterTwoFactorAuthenticationToken,
              PhotoId: response?.PhotoId,
              UserAccountMail: response?.UserAccountMail,
              UserCallNamePerson: response?.UserCallNamePerson,
              UserNamePerson: response?.UserNamePerson,
            );

            appDB.user = data;
            APIKey = data.APIKey.toString();
            print("APIKeyyyyyyyyyyyyyyyyyyyy:$APIKey");

            // set user Credential
            appDB.userCredential = UserCredentialModel(username: username, password: password);

            // Fetch required data =================================>
            context.read<ProfileCubit>().profileDataApiCall(context: context);
            print(appDB.user?.profileData?.isApiCallDone);
            print("===> profile api call");

            AppNavigation.nextScreen(
                context,
                BottomBarScreen(
                  isFromFirstTime: isFromScannerScreen,
                  isApiCall: true,
                ));
            print("response =====>${appDB.user?.personId}");
            print("app db data  =====>${appDB.user?.personId}");
            print("app db data  =====>${appDB.user?.APIKey}");
            print("response =====>${data.AppFunctionRightsList}");
          }
        } else {
          Loader.closeLoadingDialog(navigatorKey.currentContext!);
          if (isFromPinScreen) {
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (context) {
                return CustomAlertDialog(
                  context: context,
                  title: AppLocalizations.of(context)!.invalidCredentialsText,
                  message: AppLocalizations.of(context)!.inCorrectUserNamePassword,
                  onOkPressed: () {
                    Navigator.pop(context);
                  },
                );
              },
            );
          } else {
            // customSnackBar(
            //   context: navigatorKey.currentContext!,
            //   message: AppLocalizations.of(navigatorKey.currentContext!)!.inCorrectUserNamePassword,
            //   actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
            // );
          }
        }
      } else {
        print("else =====>");
        Loader.closeLoadingDialog(navigatorKey.currentContext!);
        // customSnackBar(
        //   context: navigatorKey.currentContext!,
        //   message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        //   actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
        // );
      }
    }
  }
}
