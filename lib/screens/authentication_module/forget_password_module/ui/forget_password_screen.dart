import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

import '../../../../main.dart';
import '../../../../utils/appsize.dart';
import '../../login_module/widget/common_textfield.dart';
import '../../widget/common_button.dart';
import '../bloc/forget_password_cubit.dart';

class ForgetPasswordScreen extends StatelessWidget {
  ForgetPasswordScreen({Key? key}) : super(key: key);
  final TextEditingController userNameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primaryColor,
      appBar: AppBar(
        title: Text(
          AppLocalizations.of(context)!.passwordReset,
          style: context.textTheme.titleLarge?.copyWith(fontSize: AppSize.sp16, color: AppColors.white),
        ),
        elevation: 0,
      ),
      body: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(top: AppSize.h70),
                child: Text(
                  AppLocalizations.of(context)!.forgetYourPassword,
                  textAlign: TextAlign.center,
                  style: context.textTheme.titleLarge?.copyWith(color: AppColors.white),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: AppSize.h20),
                child: Text(
                  AppLocalizations.of(context)!.forgetPasswordText,
                  style: context.textTheme.titleMedium?.copyWith(color: AppColors.white, fontSize: AppSize.sp13),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(top: AppSize.h50),
                child: CommonTextField(
                  hintText: AppLocalizations.of(context)!.userName,
                  controller: userNameController,
                ),
              ),
              SpaceV(AppSize.h10),
              CommonButton(
                buttonColor: AppColors.darkGreenColor,
                onPressed: () {
                  final forgetPasswordBloc =
                      BlocProvider.of<ForgetPasswordCubit>(navigatorKey.currentContext!, listen: false);
                  forgetPasswordBloc.resetPassword(context: context, password: '', userName: userNameController.text);
                },
                title: AppLocalizations.of(context)!.resetPassword.toUpperCase(),
                borderRadius: 0,
                height: AppSize.h30,
                titleStyle: context.textTheme.titleSmall?.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
