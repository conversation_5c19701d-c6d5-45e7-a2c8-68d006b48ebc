import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

import '../../../../common/custom_widgets/common_snackbar.dart';
import '../../../../common/custom_widgets/custom_loader.dart';
import '../../../../main.dart';
import '../repository/forget_pasword_api_repository.dart';

part 'forget_password_state.dart';

class ForgetPasswordCubit extends Cubit<ForgetPasswordState> {
  ForgetPasswordCubit() : super(ForgetPasswordInitial());

  Future<void> resetPassword({
    required BuildContext context,
    required String userName,
    required String password,
  }) async {
    if (userName.isEmpty) {
      customSnackBar(
        context: context,
        message: 'LOGIN.RESET.EMPTY',
        actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
      );
      // _formKey.currentState?.save();
    } else {
      Loader.showLoaderDialog(navigatorKey.currentContext!);
      print("api started =====>");
      FocusManager.instance.primaryFocus?.unfocus();

      final ResetPasswordApiRepository resetPasswordApiRepository = ResetPasswordApiRepository();
      final response =
          await resetPasswordApiRepository.resetPasswordApi(context: context, userName: userName, password: password);
      print("api done =====>${response}");
      //EasyLoading.dismiss();
      Loader.closeLoadingDialog(navigatorKey.currentContext!);

      print("response statusCode =====>${response?.statusCode}");

      if (response?.statusCode == 200 && response?.done == true && userName != '') {
        print("api done =====>${response}");
        print("is password changed =====>${response?.done}");
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(context)!.passwordChangedText,
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
        );
      } else if (response?.done == false && userName != '') {
        print("is password changed =====>${response?.done}");
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: response!.result[0].toString(),
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
        );
      } else {
        customSnackBar(
          context: navigatorKey.currentContext!,
          message: AppLocalizations.of(context)!.forgetErrorText,
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase(),
        );
      }
    }
  }
}
