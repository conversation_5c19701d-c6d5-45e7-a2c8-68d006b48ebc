import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/utils/appsize.dart';

import '../bloc/profile_screen_cubit.dart';
import '../widget/common/common_info_row.dart';

class ContactInfoScreen extends StatefulWidget {
  const ContactInfoScreen({super.key});

  @override
  State<ContactInfoScreen> createState() => _ContactInfoScreenState();
}

class _ContactInfoScreenState extends State<ContactInfoScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await context.read<ProfileCubit>().profileDataApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    print('appDB.user?.profileData?.media? ---->${appDB.user?.profileData?.media}');
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.contactInformationText),
      body: Padding(
          padding: EdgeInsets.only(top: AppSize.sp20, left: AppSize.sp20, right: AppSize.sp20),
          child: BlocBuilder<ProfileCubit, bool>(
            builder: (context, state) {
              print("build UI");
              if (state == true && !(appDB.user?.profileData?.isApiCallDone ?? false)) {
                return ListView.builder(
                  itemCount: 10,
                  itemBuilder: (context, index) {
                    return ShimmerWidget(
                      margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                      height: AppSize.h20,
                    );
                  },
                );
              } else if (appDB.user?.profileData?.media == null || appDB.user?.profileData?.media == '') {
                return Container(
                    height: MediaQuery.of(context).size.height * 2,
                    width: MediaQuery.of(context).size.width,
                    child: Center(child: Text(AppLocalizations.of(context)!.noDataFound)));
              } else {
                return ListView.builder(
                  itemCount: appDB.user?.profileData?.media?.length,
                  itemBuilder: (context, index) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (index == 0) ...{
                          SpaceV(AppSize.h10),
                        },
                        CommonInfoRow(
                            title:
                                '${(appDB.user?.profileData?.media?[index].mediaType) ?? "-"} - ${(appDB.user?.profileData?.media?[index].roleType) ?? "-"}',
                            value: '${(appDB.user?.profileData?.media?[index].media) ?? "-"}'),
                      ],
                    );
                  },
                );
              }
            },
          )),
    );
  }
}
