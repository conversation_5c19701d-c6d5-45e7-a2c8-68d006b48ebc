import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/profile_module/bloc/profile_screen_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../../widget/common/common_textfield.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  TextEditingController currentPasswordController = TextEditingController();
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProfileCubit, bool>(
      builder: (ctx, state) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: CustomAppBar(title: AppLocalizations.of(context)!.changePasswordText),
          body: Padding(
            padding: const EdgeInsets.only(top: 20, left: 20, right: 20),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  SpaceV(AppSize.h10),
                  Text(
                    AppLocalizations.of(context)!.changePasswordInfoText,
                    style: context.textTheme.headlineSmall!
                        .copyWith(fontSize: AppSize.sp15, color: context.themeColors.textColor),
                  ),
                  SpaceV(AppSize.h6),
                  CustomPasswordTextField(
                    controller: currentPasswordController,
                    hintText: AppLocalizations.of(context)!.currentPasswordText,
                  ),
                  SpaceV(AppSize.h6),
                  CustomPasswordTextField(
                    controller: newPasswordController,
                    hintText: AppLocalizations.of(context)!.newPasswordText,
                  ),
                  SpaceV(AppSize.h6),
                  CustomPasswordTextField(
                    controller: confirmPasswordController,
                    hintText: AppLocalizations.of(context)!.confirmNewPasswordText,
                  ),
                  SpaceV(AppSize.h10),
                  ReusableContainerButton(
                      borderRadius: BorderRadius.zero,
                      height: AppSize.h36,
                      onPressed: () {
                          ctx.read<ProfileCubit>().changePassword(
                            context: context,
                            currentPassword: currentPasswordController.text.trim(),
                            newPassword: newPasswordController.text.trim(),
                            confirmPassword: confirmPasswordController.text.trim(),
                          );
                        FocusManager.instance.primaryFocus?.unfocus();
                      },
                      buttonText: AppLocalizations.of(context)!.saveButtonText.toUpperCase(),
                      textStyle: context.textTheme.bodyLarge!.copyWith(color: AppColors.white))
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
