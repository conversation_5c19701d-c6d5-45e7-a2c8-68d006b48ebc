import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/home_module/ui/home_screen.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:url_launcher/url_launcher.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  static formatDate(DateTime date) {
    final formatter = DateFormat('EEEE, MMMM d, y', appDB.language);
    return formatter.format(date);
  }

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> {

  final newsBloc = BlocProvider.of<NewsCubit>(navigatorKey.currentContext!, listen: false);

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (newsBloc.newsList.isEmpty) {
      newsBloc.newsApiCall(context: context);
    }
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.homeBackgroundColor,
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.newsAppbarText,
            style: context.textTheme.headlineLarge
                ?.copyWith(color: Colors.white, fontSize: AppSize.sp17, fontWeight: FontWeight.w500)),
      ),
      body: RefreshIndicator(
        onRefresh: () {
          return newsBloc.newsApiCall(context: context);
        },
        child: ValueListenableBuilder(
          valueListenable: newsBloc.isLoading,
          builder: (BuildContext context, isLoading, Widget? child) {
            if (!isLoading) {
              return ScrollablePositionedList.builder(
                itemScrollController: HomeScreen.itemScrollController,
                itemCount: newsBloc.newsList.length,
                itemBuilder: (context, index) {
                  bool? isVideo = newsBloc.newsList[index].content?.contains('iframe');
                  // HtmlWidget(
                  //   '<iframe src="https://www.youtube-nocookie.com/embed/1uZssuV-igc" width="100%" height="100%" frameborder="0"></iframe>',
                  // ),
                  return Container(
                    margin: EdgeInsets.only(
                        top: index == 0 ? AppSize.sp28 : AppSize.sp10,
                        bottom: AppSize.sp10,
                        left: AppSize.sp22,
                        right: AppSize.sp22),
                    padding: EdgeInsets.only(
                        left: AppSize.sp14, right: AppSize.sp14, top: AppSize.sp14, bottom: AppSize.sp14),
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                        color: context.themeColors.homeContainerColor,
                        boxShadow: [
                          BoxShadow(
                            color: context.themeColors.homeShadowColor,
                            //spreadRadius: 5,
                            blurRadius: 2,
                            offset: Offset(0, 2),
                          )
                        ],
                        borderRadius: BorderRadius.circular(AppSize.r4)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(newsBloc.newsList[index].author ?? '',
                            style: context.textTheme.bodyMedium?.copyWith(color: context.themeColors.iconColor)),
                        SpaceV(AppSize.sp2),
                        Text(NewsScreen.formatDate(newsBloc.newsList[index].startDate ?? DateTime.now()),
                            style: context.textTheme.bodyMedium
                                ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.iconColor)),
                        SpaceV(AppSize.sp14),
                        Text(newsBloc.newsList[index].title ?? '',
                            style: context.textTheme.bodyLarge
                                ?.copyWith(fontSize: AppSize.sp18, color: context.themeColors.textColor)),
                        SpaceV(AppSize.sp15),
                        Text(newsBloc.newsList[index].intro ?? "",
                            style: context.textTheme.bodyMedium
                                ?.copyWith(fontSize: AppSize.sp12, color: context.themeColors.iconColor)),
                        // isVideo
                        //     ?
                        HtmlWidget(
                          newsBloc.newsList[index].content?.replaceAll('<p><br></p>', '') ?? '',
                          textStyle: context.textTheme.bodyMedium
                              ?.copyWith(fontSize: AppSize.sp12, color: context.themeColors.iconColor),
                          onTapUrl: (url) async {
                            await customLaunchURL(url);
                            return true;
                          },
                        ),

                        (newsBloc.newsList[index].filesHtml != null)
                            ? Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Ionicons.attach,
                                        color: context.themeColors.iconColor,
                                        size: AppSize.sp25,
                                      ),
                                      Expanded(
                                        child: Padding(
                                          padding: EdgeInsets.only(bottom: AppSize.sp12),
                                          child: Html(
                                            data:
                                                """${newsBloc.newsList[index].filesHtml?.replaceAll('<p><br></p>', '')}""",
                                            onLinkTap: (url, attributes, element) {
                                              print("pdf open ========>${newsBloc.newsList[index].filesHtml}");
                                              RegExp exp = RegExp(r'FileId=([0-9a-f-]+)');
                                              Match? match = exp.firstMatch(newsBloc.newsList[index].filesHtml ?? '');
                                              String? fileId = match?.group(1)!;
                                              print("FileId: $fileId");

                                              newsBloc.newsPdfOpenApi(context: context, guid: fileId ?? '');
                                            },
                                            style: {
                                              "p": Style(
                                                margin: Margins.all(-16),
                                                padding: HtmlPaddings(left: HtmlPadding(AppSize.sp8)),
                                              ),
                                              "*": Style(
                                                color: context.themeColors.primaryColor,
                                              ),
                                              "a": Style(
                                                  fontSize: FontSize(AppSize.sp14),
                                                  textDecoration: TextDecoration.none),
                                            },
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              )
                            : Container(),
                      ],
                    ),
                  );
                },
              );
            } else {
              return Column(
                children: [
                  SpaceV(AppSize.h20),
                  Expanded(
                    child: ListView.builder(
                      itemBuilder: (context, index) {
                        return Container(
                          margin: EdgeInsets.only(
                              top: AppSize.sp4, bottom: AppSize.sp10, left: AppSize.sp22, right: AppSize.sp22),
                          padding: EdgeInsets.only(left: AppSize.sp14, right: AppSize.sp14, top: AppSize.sp14),
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color: context.themeColors.homeContainerColor,
                              boxShadow: [
                                BoxShadow(
                                  color: context.themeColors.homeShadowColor,
                                  //spreadRadius: 5,
                                  blurRadius: 2,
                                  offset: Offset(0, 2),
                                )
                              ],
                              borderRadius: BorderRadius.circular(AppSize.r4)),
                          child: ShimmerWidget(
                            margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                            height: AppSize.h10,
                            child: Column(
                              children: [
                                Container(
                                  margin: EdgeInsets.only(bottom: AppSize.sp4, right: AppSize.sp10),
                                  height: AppSize.h10,
                                  color: AppColors.white,
                                ),
                                Container(
                                  margin: EdgeInsets.only(bottom: AppSize.sp6, right: AppSize.sp10),
                                  height: AppSize.h10,
                                  color: AppColors.white,
                                ),
                                SpaceV(AppSize.h16),
                                Container(
                                  margin: EdgeInsets.only(bottom: AppSize.sp10, right: AppSize.sp10),
                                  height: AppSize.h60,
                                  color: AppColors.white,
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  Future customLaunchURL(String url) async {
    String correctedUrl = url.startsWith("http") ? url : "https://" + url;
    try {
      log("URL : $correctedUrl");
      await launchUrl(Uri.parse(correctedUrl));
    } catch (e) {
      try {
        await launchUrl(Uri.parse(correctedUrl));
      } catch (e) {
        throw 'Could not launch $correctedUrl, error: ${e.toString()}';
      }
    }
  }
}
