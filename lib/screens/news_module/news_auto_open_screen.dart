import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/news_module/bloc/news_screen_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class NewsAutoOpenScreen extends StatefulWidget {
  const NewsAutoOpenScreen({super.key});

  static formatDate(DateTime date) {
    final formatter = DateFormat('EEEE, MMMM d, y', appDB.language);
    return formatter.format(date);
  }

  @override
  State<NewsAutoOpenScreen> createState() => _NewsAutoOpenScreenState();
}

class _NewsAutoOpenScreenState extends State<NewsAutoOpenScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final newsBloc = BlocProvider.of<NewsCubit>(context);
      await newsBloc.newsApiCall(context: context);
    });
  }

  @override
  Widget build(BuildContext context) {
    final newsBloc = BlocProvider.of<NewsCubit>(context);
    return Scaffold(
      backgroundColor: context.themeColors.homeBackgroundColor,
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.newsAppbarText,
        actions: true,
        actionList: [
          IconButton(
              onPressed: () {
                AppNavigation.previousScreen(context);
              },
              icon: Icon(Icons.close))
        ],
        automaticallyImplyLeading: false,
        isLeading: true,
      ),
      body: ValueListenableBuilder(
        valueListenable: newsBloc.isLoading,
        builder: (BuildContext context, isLoading, Widget? child) {
          if (!isLoading) {
            return Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: newsBloc.newsAutoOpenList.length,
                    itemBuilder: (context, index) {
                      bool? isVideo = newsBloc.newsAutoOpenList[index].content?.contains('iframe');
                      // HtmlWidget(
                      //   '<iframe src="https://www.youtube-nocookie.com/embed/1uZssuV-igc" width="100%" height="100%" frameborder="0"></iframe>',
                      // ),
                      log("newsAutoOpenList: ${newsBloc.newsAutoOpenList[index].content?.replaceAll('<p><br></p>', '')}");
                      return Container(
                        margin: EdgeInsets.only(
                            top: index == 0 ? AppSize.sp28 : AppSize.sp10,
                            bottom: AppSize.sp10,
                            left: AppSize.sp22,
                            right: AppSize.sp22),
                        padding: EdgeInsets.only(
                            left: AppSize.sp14, right: AppSize.sp14, top: AppSize.sp14, bottom: AppSize.sp14),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            color: context.themeColors.homeContainerColor,
                            boxShadow: [
                              BoxShadow(
                                color: context.themeColors.homeShadowColor,
                                //spreadRadius: 5,
                                blurRadius: 2,
                                offset: Offset(0, 2),
                              )
                            ],
                            borderRadius: BorderRadius.circular(AppSize.r4)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(newsBloc.newsAutoOpenList[index].author ?? '',
                                style: context.textTheme.bodyMedium?.copyWith(color: context.themeColors.iconColor)),
                            SpaceV(AppSize.sp2),
                            Text(
                                NewsAutoOpenScreen.formatDate(
                                    newsBloc.newsAutoOpenList[index].startDate ?? DateTime.now()),
                                style: context.textTheme.bodyMedium
                                    ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.iconColor)),
                            SpaceV(AppSize.sp14),
                            Text(newsBloc.newsAutoOpenList[index].title ?? '',
                                style: context.textTheme.bodyLarge
                                    ?.copyWith(fontSize: AppSize.sp18, color: context.themeColors.textColor)),
                            SpaceV(AppSize.sp15),
                            Text(newsBloc.newsAutoOpenList[index].intro ?? "",
                                style: context.textTheme.bodyMedium
                                    ?.copyWith(fontSize: AppSize.sp12, color: context.themeColors.iconColor)),
                            // isVideo
                            //     ?
                            HtmlWidget(
                              newsBloc.newsAutoOpenList[index].content?.replaceAll('<p><br></p>', '') ?? '',
                              textStyle: context.textTheme.bodyMedium
                                  ?.copyWith(fontSize: AppSize.sp12, color: context.themeColors.iconColor),
                            ),

                            (newsBloc.newsAutoOpenList[index].filesHtml != null)
                                ? Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Icon(
                                            Ionicons.attach,
                                            color: context.themeColors.iconColor,
                                            size: AppSize.sp25,
                                          ),
                                          Expanded(
                                            child: Padding(
                                              padding: EdgeInsets.only(bottom: AppSize.sp12),
                                              child: Html(
                                                data:
                                                    """${newsBloc.newsAutoOpenList[index].filesHtml?.replaceAll('<p><br></p>', '')}""",
                                                onLinkTap: (url, attributes, element) {
                                                  print(
                                                      "pdf open ========>${newsBloc.newsAutoOpenList[index].filesHtml}");
                                                  RegExp exp = RegExp(r'FileId=([0-9a-f-]+)');
                                                  Match? match =
                                                      exp.firstMatch(newsBloc.newsAutoOpenList[index].filesHtml ?? '');
                                                  String? fileId = match?.group(1)!;
                                                  print("FileId: $fileId");

                                                  newsBloc.newsPdfOpenApi(context: context, guid: fileId ?? '');
                                                },
                                                style: {
                                                  "p": Style(
                                                    margin: Margins.all(-16),
                                                    padding: HtmlPaddings(left: HtmlPadding(AppSize.sp8)),
                                                  ),
                                                  "*": Style(
                                                    color: context.themeColors.primaryColor,
                                                  ),
                                                  "a": Style(
                                                      fontSize: FontSize(AppSize.sp14),
                                                      textDecoration: TextDecoration.none),
                                                },
                                              ),
                                            ),
                                          )
                                        ],
                                      ),
                                    ],
                                  )
                                : Container(),
                          ],
                        ),
                      );
                    },
                  ),
                ),
                Padding(
                  padding:
                      EdgeInsets.only(left: AppSize.sp6, right: AppSize.sp6, top: AppSize.sp6, bottom: AppSize.h24),
                  child: ReusableContainerButton(
                      height: AppSize.h32,
                      borderRadius: BorderRadius.circular(2),
                      onPressed: () {
                        newsBloc.autoOpenNewsRead(context: context).then(
                          (value) {
                            AppNavigation.previousScreen(context);
                          },
                        );
                      },
                      width: MediaQuery.of(context).size.width * 0.9,
                      buttonText: AppLocalizations.of(context)!.okUnderstood.toUpperCase(),
                      textStyle: context.textTheme.bodyLarge!.copyWith(color: AppColors.white)),
                ),
              ],
            );
          } else {
            return Column(
              children: [
                SpaceV(AppSize.h20),
                Expanded(
                  child: ListView.builder(
                    itemBuilder: (context, index) {
                      return Container(
                        margin: EdgeInsets.only(
                            top: AppSize.sp4, bottom: AppSize.sp10, left: AppSize.sp22, right: AppSize.sp22),
                        padding: EdgeInsets.only(left: AppSize.sp14, right: AppSize.sp14, top: AppSize.sp14),
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                            color: context.themeColors.homeContainerColor,
                            boxShadow: [
                              BoxShadow(
                                color: context.themeColors.homeShadowColor,
                                //spreadRadius: 5,
                                blurRadius: 2,
                                offset: Offset(0, 2),
                              )
                            ],
                            borderRadius: BorderRadius.circular(AppSize.r4)),
                        child: ShimmerWidget(
                          margin: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                          height: AppSize.h10,
                          child: Column(
                            children: [
                              Container(
                                margin: EdgeInsets.only(bottom: AppSize.sp4, right: AppSize.sp10),
                                height: AppSize.h10,
                                color: AppColors.white,
                              ),
                              Container(
                                margin: EdgeInsets.only(bottom: AppSize.sp6, right: AppSize.sp10),
                                height: AppSize.h10,
                                color: AppColors.white,
                              ),
                              SpaceV(AppSize.h16),
                              Container(
                                margin: EdgeInsets.only(bottom: AppSize.sp10, right: AppSize.sp10),
                                height: AppSize.h60,
                                color: AppColors.white,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}
