import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:staff_medewerker/app/db/model/download_file_model.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/news_module/model/news_model.dart';
import 'package:staff_medewerker/screens/news_module/repository/news_api_repository.dart';
import 'package:staff_medewerker/screens/news_module/repository/news_auto_open_api_repository.dart';
import 'package:staff_medewerker/screens/profile_module/profile_image_repository/profile_image_api_repository.dart';

class NewsCubit extends Cubit<bool> {
  NewsCubit() : super(false);
  ValueNotifier<bool> isLoading = ValueNotifier(true);
  ValueNotifier<bool> isProfileLoading = ValueNotifier(true);
  ValueNotifier<bool> isAutoOpenNewsLoading = ValueNotifier(false);
  ValueNotifier<bool> isClockingIn = ValueNotifier(false);

  List<NewsModel> newsList = [];
  List<NewsModel> newsList1 = [];
  List<NewsModel> newsAutoOpenList = [];
  List<DownloadFileModel> downLoadImageList = [];
  List<DownloadFileModel> downLoadImageList1 = [];
  List<Widget> imageWidgets = [];
  Map<String, Image> authorIdToImage = {};

  final NewsApiRepository newsApiRepository = NewsApiRepository();
  final ProfileImageApiRepository profileImageApiRepository = ProfileImageApiRepository();

  Future<void> newsApiCall({required BuildContext context}) async {
    newsList.clear();
    isLoading.value = true;
    try {
      log("new API started =====>");
      final response = await newsApiRepository.newsApi(context: context);
      log("API done =====>${response}");
      if (response != null) {
        newsList = response;
      } else {}
    } on Exception {}
    isLoading.value = false;
  }

  Future<void> newsUserProfileApiCall({required BuildContext context}) async {
    imageWidgets.clear();
    if (newsList.isNotEmpty) {
      final imageList = <Widget>[]; // Change to Widget type
      for (final newsItem in newsList) {
        final authorId = newsItem.authorId ?? '';
        if (authorIdToImage.containsKey(authorId)) {
          final imageWidget = authorIdToImage[authorId];
          if (imageWidget != null) {
            imageList.add(imageWidget);
          } else {
            imageList.add(Container());
          }
        } else {
          final response = await newsApiRepository.newsDocumentApiCall(context: context, guid: authorId);
          if (response != null && response.fileContent != null && response.fileContent!.isNotEmpty) {
            final encodedStr = response.fileContent!;
            Uint8List bytes = base64.decode(encodedStr);
            Image image = Image.memory(bytes);
            authorIdToImage[authorId] = image;
            imageList.add(image);
            newsItem.isImageAvailable = true;
          } else {
            // If fileContent is empty or null, add initials person name instead
            String? initials = newsItem.initialsPerson;
            // You can customize the style of how the initials are displayed
            Widget initialsWidget = Text(
              initials ?? '-',
              style: TextStyle(
                fontSize: 24, // adjust the font size as needed
                // add more styling properties as needed
              ),
            );
            imageList.add(initialsWidget);
            newsItem.isImageAvailable = false;
          }
        }
      }
      imageWidgets = imageList;
    }
  }

  // Future<void> newsUserProfileApiCall({required BuildContext context}) async {
  //   imageWidgets.clear();
  //   if (newsList.isNotEmpty) {
  //     final imageList = <Image?>[];
  //     for (final newsItem in newsList) {
  //       final authorId = newsItem.authorId ?? '';
  //       if (authorIdToImage.containsKey(authorId)) {
  //         log("authorIdToImage =====>$authorIdToImage");
  //
  //         imageList.add(authorIdToImage[authorId]);
  //       } else {
  //         final response1 = await newsApiRepository.newsDocumentApiCall(context: context, guid: authorId);
  //         if (response1 != null) {
  //           log("downLoadImageList =====>$downLoadImageList");
  //
  //           downLoadImageList.add(response1);
  //           if (response1.fileContent != null) {
  //             final encodedStr = response1.fileContent;
  //             Uint8List bytes = base64.decode(encodedStr!);
  //             Image image = Image.memory(bytes);
  //             authorIdToImage[authorId] = image;
  //             imageList.add(image);
  //             newsItem.isImageAvailable = true;
  //           }
  //         } else {
  //           final response2 = DownloadFileModel(name: newsItem.initialsPerson, fileContent: "");
  //           downLoadImageList.add(response2);
  //           //log("downLoadImageList else=====>$downLoadImageList");
  //           if (response2.fileContent == null || response2.fileContent != '') {
  //             newsItem.isImageAvailable = false;
  //             log("downLoadImageList else=====>${newsItem.isImageAvailable}");
  //
  //             // final encodedStr = response2.fileContent;
  //             // Uint8List bytes = base64.decode(encodedStr!);
  //             // Image image = Image.memory(bytes);
  //             // authorIdToImage[authorId] = image;
  //             // imageList.add(image);
  //           }
  //         }
  //       }
  //     }
  //     // log("map ======>1 ${authorIdToImage.toString()}");
  //     imageWidgets = imageList.where((image) => image != null).map((image) => image!).toList();
  //
  //   } else {}
  // }

  Future<void> newsAndProfileApiCall({required BuildContext context}) async {
    await newsApiCall(context: context);
    isProfileLoading.value = true;
    print("isProfileLoadingisProfileLoadingisProfileLoading${isProfileLoading.value}");
    await newsUserProfileApiCall(context: context);
    isProfileLoading.value = false;
    print("isProfileLoadingisProfileLoadingisProfileLoading${isProfileLoading.value}");
  }

  Future<void> newsPdfOpenApi({required BuildContext context, required String guid}) async {
    customSnackBar(
        snackBarDuration: 5000,
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(context)!.openFile,
        actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    final response = await profileImageApiRepository.getProfilePicApi(
      context: context,
      guid: guid,
    );
    String? path;
    if (Platform.isAndroid) {
      await getTemporaryDirectory().then((value) => path = value.path);
    } else if (Platform.isIOS) {
      await getTemporaryDirectory().then((value) => path = value.path);
    }
    final encodedStr = response?.fileContent;
    Uint8List bytes = base64.decode(encodedStr ?? '');
    String filePath = '$path/${response?.fileName}';
    File file = File(filePath);
    await file.writeAsBytes(bytes);
    await OpenFile.open(file.path);
    ScaffoldMessenger.of(navigatorKey.currentContext!).hideCurrentSnackBar();
  }

  Future<void> newsAutoOpenApiCall({required BuildContext context}) async {
    newsAutoOpenList.clear();
    isAutoOpenNewsLoading.value = true;
    try {
      log("API started =====>");
      final NewsAutoOpenApiRepository newsAutoOpenApiRepository = NewsAutoOpenApiRepository();
      final response = await newsAutoOpenApiRepository.newsAutoOpenApi(context: context);
      isAutoOpenNewsLoading.value = false;
      log("auto news API done =====>${response}");
      // newsAutoOpenList = response!;
      if (response != null) {
        newsAutoOpenList = response;
      } else {}
    } on Exception {}
  }

  Future<void> autoOpenNewsRead({required BuildContext context}) async {
    try {
      log("API started =====>");
      final NewsAutoOpenApiRepository newsAutoOpenApiRepository = NewsAutoOpenApiRepository();
      final response = await newsAutoOpenApiRepository.autoOpenNewsRead(context: context);
      if (response != null && response.done == true) {
      } else {}
    } on Exception {}
  }
}
