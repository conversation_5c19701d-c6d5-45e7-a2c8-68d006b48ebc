import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/clocking_module/models/clocking_status.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clock_in_api_repository.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clock_out_api_repository.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/clocking_status_api_repository.dart';
import 'package:staff_medewerker/screens/clocking_module/repository/cost_centers_non_time_bound_person.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_activity_screen/bloc/select_activity_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_department_screen/bloc/select_department_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/activitylist_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/cost_centers_non_time_bound_person.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/departmentlist_response_model.dart';

part 'clocking_screen_state.dart';

class ClockingCubit extends Cubit<ClockingState> {
  ClockingCubit() : super(ClockingInitial());

  ValueNotifier<bool> isClockingIn = ValueNotifier(false);

  ValueNotifier<String> selectedDepartmentName = ValueNotifier('');
  ValueNotifier<String> selectedDepartmentId = ValueNotifier('');

  ValueNotifier<String> selectedActivityName = ValueNotifier('');
  ValueNotifier<String> selectedActivityId = ValueNotifier('');
  ValueNotifier<bool> isLoading = ValueNotifier(false);
  // ValueNotifier<bool> isTimerLoading = ValueNotifier(false);
  ValueNotifier<bool> isButtonLoading = ValueNotifier(false);
  TextEditingController remarkTextController = TextEditingController();
  ValueNotifier<int> totalTrackedHours = ValueNotifier(00);
  ValueNotifier<int> totalTrackedMinutes = ValueNotifier(00);
  Timer? timer;

  List<CostCentersNonTimeBoundPersonModel> costCentersNonTimeBoundPersonList = [];
  bool firstTimeApiCall = false;

  var clockInResponse;
  var clockOutResponse;
  ValueNotifier<ClockingStatusModel> clockStatusResponse = ValueNotifier(
    ClockingStatusModel(),
  );

  Future<void> clockingStatusApiCall({required BuildContext context}) async {
    clockStatusResponse.value.lastClockingId = "";
    print("API started =====>");
    try {
      final ClockingStatusApiRepository clockingStatusApiRepository = ClockingStatusApiRepository();
      final response = await clockingStatusApiRepository.clockingStatusApi(context: context);
      if (response != null) {
        clockStatusResponse.value = response;
        print("API done =====>${response}");

        List<DepartmentListResponseModel> departmentList = context.read<SelectDepartmentCubit>().departmentList;
        List<ActivityListResponseModel> activityList = context.read<SelectActivityCubit>().activityList;

        departmentList.forEach((element) {
          if (response.lastClockingId == null &&
              element.guid.toLowerCase() == response.defaultCostDepartmentId?.toLowerCase()) {
            selectedDepartmentId.value = element.guid;
            selectedDepartmentName.value = element.title;
            print("selectedDepartmentName1: ${selectedDepartmentName.value}");
          } else if (element.guid.toLowerCase() == response.lastCostDepartmentId?.toLowerCase()) {
            selectedDepartmentId.value = element.guid;
            selectedDepartmentName.value = element.title;
            print("selectedDepartmentName2: ${selectedDepartmentName.value}");
            return;
          }
        });
        activityList.forEach((element) {
          if (response.lastCostCenterId == null &&
              element.costCenterId.toLowerCase() == response.defaultCostCenterId?.toLowerCase()) {
            selectedActivityId.value = element.costCenterId;
            selectedActivityName.value = element.title;
            print("selectedActivityName1: ${selectedActivityName.value}");
          } else if (element.costCenterId.toLowerCase() == response.lastCostCenterId?.toLowerCase()) {
            selectedActivityId.value = element.costCenterId;
            selectedActivityName.value = element.title;
            print("selectedActivityName2: ${selectedActivityName.value}");
            return;
          }
        });
        if (selectedDepartmentId.value == "" ||
            selectedDepartmentName.value == "" ||
            selectedActivityId.value == "" ||
            selectedActivityName.value == "") {
          isClockingIn.value = false;
          selectedDepartmentId.value = departmentList.first.guid;
          selectedDepartmentName.value = departmentList.first.title;
          selectedActivityId.value = activityList.first.costCenterId;
          selectedActivityName.value = activityList.first.title;
        } else {
          isClockingIn.value = true;
        }

        print("response.lastClockingId: ${response.lastClockingId}");
        if (clockStatusResponse.value.lastClockingId == "" || clockStatusResponse.value.lastClockingId == null) {
          isClockingIn.value = false;
        } else {
          isClockingIn.value = true;
        }
      } else {
        // Handle the case when the API response is null
        print("API done with null response");
        // You can show an error message to the user or take appropriate action here.
      }
    } catch (e) {
      // Handle any potential errors that occur during the API call
      print("API error: $e");
      // You can show an error message to the user or take appropriate action here.
    } finally {}

    emit(ClockingInitial());
  }

  Future<void> clockInOutButtonClick({required BuildContext context}) async {
    isButtonLoading.value = true;
    if (!isClockingIn.value) {
      print("api started =====> in");
      final ClockingInApiRepository clockingInApiRepository = ClockingInApiRepository();
      final response = await clockingInApiRepository.clockingInApi(
          context: context,
          defaultCostDepartmentId: selectedDepartmentId.value,
          defaultCostCenterId: selectedActivityId.value);
      print("api done =====>${response?.result[0]}");

      clockInResponse = response;

      if (response?.done == true) {
        customSnackBar(
            context: navigatorKey.currentContext!,
            message: "${response?.result[0]}",
            actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
        isClockingIn.value = isClockingIn.value == true ? false : true;
        await clockingStatusApiCall(context: context);
      }
      totalTrackedHours.value = 0;
      totalTrackedMinutes.value = 0;
      totalTrackedTimer();
      // else clock out
    } else {
      print("api started CLOCK OUT=====>");
      final ClockingOutApiRepository clockingOutApiRepository = ClockingOutApiRepository();
      final activityBloc = BlocProvider.of<SelectActivityCubit>(context);
      List<String> nonTimeBoundCostCenterIds = [];
      activityBloc.timeBondFalseList.forEach((element) {
        if (element.isSelected == true) {
          nonTimeBoundCostCenterIds.add(element.costCenterId);
        }
      });

      final response = await clockingOutApiRepository.clockingOutApi(
          context: context,
          lastClockingId: clockStatusResponse.value.lastClockingId ?? "",
          nonTimeBoundCostCenterIds: nonTimeBoundCostCenterIds,
          remark: remarkTextController.text);
      print("api done =====>${response?.result[0]}");
      clockOutResponse = response;

      isClockingIn.value = isClockingIn.value == false ? true : false;
      clockingStatusApiCall(context: context);
      customSnackBar(
          context: context,
          message: "${response?.result[0]}",
          actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    timer?.cancel();

    isButtonLoading.value = false;
    emit(ClockingInitial());
  }

  updateActivityValue(String value, id) {
    selectedActivityName.value = value;
    selectedActivityId.value = id;
    emit(ClockingInitial());
  }

  updateDepartmentValue(value, id) {
    selectedDepartmentName.value = value;
    selectedDepartmentId.value = id;
    emit(ClockingInitial());
  }

  String calculateTimeDifference(String timeString1, String timeString2) {
    DateFormat timeFormat = DateFormat('HH:mm');
    DateTime time1 = timeFormat.parse(timeString1);
    DateTime time2 = timeFormat.parse(timeString2);
    Duration difference = time1.difference(time2);
    return "${difference.inHours}:${difference.inMinutes.remainder(60)}";
  }

  // void totalTrackedTimer() {
  //   timer = Timer.periodic(Duration(minutes: 1), (timer) {
  //     DateFormat formatter = DateFormat("HH:mm");
  //     String formattedTime = formatter.format(DateTime.now());
  //     print("formattedTime $formattedTime");
  //
  //     String formattedDifference =
  //         calculateTimeDifference(formattedTime, clockStatusResponse.value.lastTimeFrom ?? "00:00");
  //     List<int> timeDifferenceParts = formattedDifference.split(':').map((part) => int.parse(part)).toList();
  //
  //     totalTrackedHours.value = timeDifferenceParts[0];
  //     totalTrackedMinutes.value = timeDifferenceParts[1];
  //
  //     if (totalTrackedMinutes.value >= 60) {
  //       totalTrackedMinutes.value = 0;
  //       totalTrackedHours.value += 1;
  //     } else {
  //       totalTrackedMinutes.value++;
  //     }
  //
  //     print("${totalTrackedHours.value}:${totalTrackedMinutes.value}");
  //   });
  // }

  void totalTrackedTimer() {
    // Call the function initially
    updateTotalTrackedTime();

    // Start the timer with 1-minute intervals
    timer = Timer.periodic(Duration(minutes: 1), (timer) {
      updateTotalTrackedTime();
    });
  }

  void updateTotalTrackedTime({bool firstCall = false}) {
    DateFormat formatter = DateFormat("HH:mm");
    String formattedTime = formatter.format(DateTime.now());
    print("formattedTime $formattedTime");
    print("formattedTime ${clockStatusResponse.value.lastTimeFrom}");

    String formattedDifference =
        calculateTimeDifference(formattedTime, clockStatusResponse.value.lastTimeFrom ?? "00:00");
    print("formattedDifference $formattedDifference");

    List<int> timeDifferenceParts = formattedDifference.split(':').map((part) => int.parse(part)).toList();

    totalTrackedHours.value = timeDifferenceParts[0];
    totalTrackedMinutes.value = timeDifferenceParts[1];

    print("${totalTrackedHours.value}:${totalTrackedMinutes.value}");
  }

  Future<void> costCentersNonTimeBoundPersonApiCall(
      {required BuildContext context, required String iSOStartDate}) async {
    print("api started CLOCK OUT=====>");
    final CostCentersNonTimeBoundPersonApiRepository costCentersNonTimeBoundPersonApiRepository =
        CostCentersNonTimeBoundPersonApiRepository();
    final response = await costCentersNonTimeBoundPersonApiRepository.costCentersNonTimeBoundPersonApi(
        context: context, startIsoDate: iSOStartDate);
    clockOutResponse = response;

    if (response!.isNotEmpty) {
      costCentersNonTimeBoundPersonList.clear();
      costCentersNonTimeBoundPersonList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
  }

  Future<void> firstTimeAllApiCall({required BuildContext context, bool isFirstTime = false}) async {
    if (isFirstTime && firstTimeApiCall == false) {
      isLoading.value = true;
    }

    // if (firstTimeApiCall == false) {
    await context.read<SelectDepartmentCubit>().departmentApiCall(context: context);
    String iSODate =
        "${DateTime.now().year}${DateFormatFunctions.formatDay(DateTime.now().month)}${DateFormatFunctions.formatDay(DateTime.now().day)}";
    
    await context.read<SelectActivityCubit>().activityExclusionsApiCall(context: context).then((value) async {
    
    });

    final activityBloc = BlocProvider.of<SelectActivityCubit>(context);
    await context.read<SelectActivityCubit>().costCentersActivityApiCall(context: context, selectedDate: iSODate);

    DateFormat dateFormat = DateFormat('yyyyMMdd');

    String formattedDate = dateFormat.format(DateTime.now());

    print(formattedDate); // Output: 20231102
    await costCentersNonTimeBoundPersonApiCall(context: context, iSOStartDate: formattedDate);

    firstTimeApiCall = true;

    await clockingStatusApiCall(context: context);

    totalTrackedTimer();

    if (isFirstTime) {
      isLoading.value = false;
    }
  }

  void changeValue({required BuildContext context, bool? value, required int index}) {
    final activityBloc = BlocProvider.of<SelectActivityCubit>(context);
    activityBloc.timeBondFalseList[index].isSelected = value ?? false;
    emit(ClockingInitial());
  }

  //
  // void changeLunchValue({required BuildContext context, bool? lunchValue}) {
  //   checkBox1.value = lunchValue ?? false;
  //   emit(ClockingInitial());
  // }
  //
  // void changeMealValue({required BuildContext context, bool? mealValue}) {
  //   checkBox2.value = mealValue ?? false;
  //   emit(ClockingInitial());
  // }
  //
  // void changeSurchargeValue({required BuildContext context, bool? surchargeValue}) {
  //   checkBox3.value = surchargeValue ?? false;
  //   emit(ClockingInitial());
  // }
  //
  // void changeWorkFromHomeValue({required BuildContext context, bool? workFromHomeValue}) {
  //   checkBox4.value = workFromHomeValue ?? false;
  //   emit(ClockingInitial());
  // }
}
