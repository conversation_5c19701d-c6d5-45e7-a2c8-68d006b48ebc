import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_textfield.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/shimmer_effect.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/authentication_module/widget/common_button.dart';
import 'package:staff_medewerker/screens/clocking_module/bloc/clocking_screen_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_activity_screen/bloc/select_activity_cubit.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_activity_screen/ui/activity_list_screen.dart';
import 'package:staff_medewerker/screens/clocking_module/ui/select_department_screen/ui/department_list_screen.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

class ClockingScreen extends StatefulWidget {
  const ClockingScreen({super.key});

  @override
  State<ClockingScreen> createState() => _ClockingScreenState();
}

class _ClockingScreenState extends State<ClockingScreen> {
  late ClockingCubit clockingBloc;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    clockingBloc = BlocProvider.of<ClockingCubit>(context);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      clockingBloc.firstTimeAllApiCall(context: context, isFirstTime: true);
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    clockingBloc.timer?.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.listGridColor1,
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.clocking),
      body: BlocBuilder<ClockingCubit, ClockingState>(
        builder: (ctx, state) {
          final clockingBloc = ctx.read<ClockingCubit>();
          return ValueListenableBuilder(
            valueListenable: clockingBloc.isLoading,
            builder: (BuildContext context1, value, Widget? child) {
              if (!value) {
                print(
                    "context.read<SelectActivityCubit>().timeBondFalseList.length${context.read<SelectActivityCubit>().timeBondFalseList.length}");
                return ValueListenableBuilder(
                  builder: (BuildContext context2, isClockedIn, Widget? child) {
                    return SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.all(AppSize.sp22),
                        child: Container(
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                color: context.themeColors.homeShadowColor,
                                blurRadius: 2,
                                offset: Offset(0, 2),
                              ),
                            ],
                            borderRadius: BorderRadius.circular(AppSize.r4),
                            color: context.themeColors.homeContainerColor,
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(AppSize.sp14),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  isClockedIn
                                      ? "${AppLocalizations.of(context)!.clockedSinceText}${clockingBloc.clockStatusResponse.value.lastTimeFrom ?? "Wait..."}"
                                      : AppLocalizations.of(context)!.notClockedText,
                                  style: context.textTheme.titleLarge!.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp22,
                                  ),
                                ),
                                isClockedIn
                                    ? Column(
                                        children: [
                                          SpaceV(AppSize.h10),
                                          ValueListenableBuilder(
                                            valueListenable: clockingBloc.totalTrackedHours,
                                            builder: (BuildContext context3, int totalTrackedHours, Widget? child) {
                                              return ValueListenableBuilder(
                                                valueListenable: clockingBloc.totalTrackedMinutes,
                                                builder:
                                                    (BuildContext context4, int totalTrackedMinutes, Widget? child) {
                                                  return Text(
                                                    "${AppLocalizations.of(context)!.clockedInTime1} ${totalTrackedHours} ${AppLocalizations.of(context)!.clockedInTime2} ${totalTrackedMinutes} ${AppLocalizations.of(context)!.clockedInTime3}",
                                                    style: context.textTheme.bodyMedium!.copyWith(
                                                        color: context.themeColors.textColor.withOpacity(0.6),
                                                        fontSize: AppSize.sp13,
                                                        fontWeight: FontWeight.w500),
                                                  );
                                                },
                                              );
                                            },
                                          ),
                                        ],
                                      )
                                    : Container(),
                                Column(
                                  children: [
                                    SpaceV(AppSize.h22),
                                    InkWell(
                                      splashColor: Colors.transparent,
                                      onTap: () async {
                                        final result = await Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => DepartmentScreen(
                                              selectedDepartmentName: clockingBloc.selectedDepartmentName.value,
                                              selectedDepartmentId: clockingBloc.selectedDepartmentId.value,
                                              // selectedDepartmentValue: "Locatie U",
                                            ),
                                          ),
                                        );
                                        if (result != null) {
                                          clockingBloc.updateDepartmentValue(
                                              result['selectedDepartmentName'], result['selectedDepartmentId']);
                                        }
                                      },
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)!.departmentText,
                                            style: context.textTheme.headlineLarge!.copyWith(
                                                color: context.themeColors.textColor,
                                                fontSize: AppSize.sp15,
                                                fontWeight: FontWeight.normal),
                                          ),
                                          Row(
                                            children: [
                                              ValueListenableBuilder(
                                                valueListenable: clockingBloc.selectedDepartmentName,
                                                builder: (BuildContext context, String value, Widget? child) {
                                                  return Text(
                                                    value,
                                                    style: context.textTheme.headlineLarge!.copyWith(
                                                        color: context.themeColors.textColor,
                                                        fontSize: AppSize.sp15,
                                                        fontWeight: FontWeight.normal),
                                                  );
                                                },
                                              ),
                                              Icon(Icons.arrow_drop_down_sharp)
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                    SpaceV(AppSize.h6),
                                    Divider(
                                      thickness: 1,
                                    ),
                                    SpaceV(AppSize.h6),
                                    InkWell(
                                        splashColor: Colors.transparent,
                                        onTap: () async {
                                          print(
                                              "clockingBloc.selectedActivityName.value ${clockingBloc.selectedActivityName.value}");
                                          print(
                                              "clockingBloc.selectedActivityName.value ${clockingBloc.selectedActivityId.value}");
                                          final result = await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) => ActivitySearchListScreen(
                                                      selectedActivityValue: clockingBloc.selectedActivityName.value,
                                                      selectedActivityId: clockingBloc.selectedActivityId.value,
                                                    )),
                                          );
                                          print(result);
                                          if (result != null) {
                                            clockingBloc.updateActivityValue(
                                                result['selectedActivityName'], result['selectedActivityId']);
                                          }
                                        },
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              AppLocalizations.of(context)!.activityText,
                                              style: context.textTheme.headlineLarge!.copyWith(
                                                  color: context.themeColors.textColor,
                                                  fontSize: AppSize.sp15,
                                                  fontWeight: FontWeight.normal),
                                            ),
                                            SpaceH(AppSize.w10),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.end,
                                              children: [
                                                Container(
                                                  alignment: Alignment.centerRight,
                                                  width: AppSize.w120,
                                                  child: Text(
                                                    clockingBloc.selectedActivityName.value,
                                                    style: context.textTheme.headlineLarge!.copyWith(
                                                        color: context.themeColors.textColor,
                                                        fontSize: AppSize.sp15,
                                                        fontWeight: FontWeight.normal),
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ),
                                                Icon(Icons.arrow_drop_down_sharp)
                                              ],
                                            )
                                          ],
                                        )),
                                    isClockedIn ? SpaceV(AppSize.h6) : Container(),
                                    isClockedIn
                                        ? Divider(
                                            thickness: 1,
                                          )
                                        : Container(),
                                    isClockedIn
                                        ? Container(
                                            height: AppSize.h200,
                                            width: double.infinity,
                                            child: ListView.builder(
                                              itemCount: context.read<SelectActivityCubit>().timeBondFalseList.length,
                                              itemBuilder: (ctx1, index) {
                                                final activityBloc = BlocProvider.of<SelectActivityCubit>(context);
                                                return CheckboxListTile(
                                                  contentPadding: EdgeInsets.zero,
                                                  title: Text(activityBloc.timeBondFalseList[index].title,
                                                      style: context.textTheme.bodyMedium?.copyWith(
                                                          color: context.themeColors.textColor,
                                                          fontSize: AppSize.sp15)),
                                                  value: activityBloc.timeBondFalseList[index].isSelected,
                                                  activeColor: AppColors.primaryColor,
                                                  onChanged: (value) {
                                                    clockingBloc.changeValue(
                                                        context: context, value: value, index: index);
                                                    print(activityBloc.timeBondFalseList[index].isSelected);
                                                  },
                                                );
                                              },
                                            ),
                                          )
                                        : Container(),
                                    isClockedIn
                                        ? Divider(
                                            thickness: 1,
                                          )
                                        : Container(),
                                    isClockedIn
                                        ? CustomTextField(
                                            controller: clockingBloc.remarkTextController,
                                            hintText: AppLocalizations.of(context)!.remark,
                                            border: InputBorder.none)
                                        : Container(),
                                    SpaceV(AppSize.h14),
                                    ValueListenableBuilder(
                                        valueListenable: clockingBloc.isButtonLoading,
                                        builder: (BuildContext context, isButtonLoading, Widget? child) {
                                          return AbsorbPointer(
                                            absorbing: isButtonLoading,
                                            child: CommonButton(
                                                buttonColor: isClockedIn
                                                    ? context.themeColors.buttonRedColor
                                                    : AppColors.primaryColor,
                                                height: AppSize.h31,
                                                borderRadius: 0,
                                                onPressed: () async {
                                                  await ctx
                                                      .read<ClockingCubit>()
                                                      .clockInOutButtonClick(context: context);
                                                },
                                                title: isClockedIn
                                                    ? AppLocalizations.of(context)!.clockOutText.toUpperCase()
                                                    : AppLocalizations.of(context)!.clockInText.toUpperCase(),
                                                titleStyle: context.textTheme.bodyLarge!.copyWith(
                                                    fontSize: AppSize.sp13,
                                                    color: AppColors.white,
                                                    fontWeight: FontWeight.w500,
                                                    letterSpacing: 1),
                                                isLoading: isButtonLoading),
                                          );
                                          // return ReusableContainerButton(
                                          //   height: AppSize.h32,
                                          //   borderRadius: BorderRadius.circular(2),
                                          //   onPressed: () async {
                                          //     await ctx.read<ClockingCubit>().clockInOutButtonClick(context: context);
                                          //   },
                                          //   width: MediaQuery.of(context).size.width * 0.86,
                                          //   backgroundColor: isClockedIn
                                          //       ? context.themeColors.buttonRedColor
                                          //       : AppColors.primaryColor,
                                          //   buttonText: isClockedIn
                                          //       ? AppLocalizations.of(context)!.clockOutText.toUpperCase()
                                          //       : AppLocalizations.of(context)!.clockInText.toUpperCase(),
                                          //   textStyle: context.textTheme.bodyLarge!.copyWith(
                                          //       fontSize: AppSize.sp13,
                                          //       color: AppColors.white,
                                          //       fontWeight: FontWeight.w500,
                                          //       letterSpacing: 1),
                                          // );
                                        })
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                  valueListenable: clockingBloc.isClockingIn,
                );
              } else {
                return Padding(
                  padding: const EdgeInsets.all(25),
                  child: Container(
                    decoration: BoxDecoration(
                      color: context.themeColors.cardColor,
                      borderRadius: BorderRadius.circular(5),
                      boxShadow: [
                        BoxShadow(
                          color: context.themeColors.cardShadowColor,
                          blurRadius: 2,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ShimmerWidget(
                            height: AppSize.h40,
                          ),
                          Column(
                            children: [
                              ShimmerWidget(
                                margin: EdgeInsets.only(top: AppSize.h26, bottom: AppSize.h10),
                                height: AppSize.h10,
                              ),
                              Divider(
                                thickness: 1,
                              ),
                              ShimmerWidget(
                                margin: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10),
                                height: AppSize.h10,
                              ),
                              Divider(
                                thickness: 1,
                              ),
                              ShimmerWidget(
                                margin: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h10),
                                height: AppSize.h10,
                              ),
                              Divider(
                                thickness: 1,
                              ),
                              ShimmerWidget(
                                margin: EdgeInsets.only(top: AppSize.h10, bottom: AppSize.h20),
                                height: AppSize.h10,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }
            },
          );
        },
      ),
    );
  }
}

// isClockedIn
// ? CheckboxListTile(
// contentPadding: EdgeInsets.zero,
// title: Text(AppLocalizations.of(context)!.lunch,
// style: context.textTheme.bodyMedium?.copyWith(
// color: context.themeColors.textColor, fontSize: AppSize.sp15)),
// value: clockingBloc.checkBox1.value,
// activeColor: AppColors.primaryColor,
// onChanged: (value) {
// clockingBloc.changeLunchValue(context: context, lunchValue: value);
// },
// )
//     : Container(),
// isClockedIn
// ? CheckboxListTile(
// contentPadding: EdgeInsets.zero,
// title: Text(AppLocalizations.of(context)!.meal,
// style: context.textTheme.bodyMedium?.copyWith(
// color: context.themeColors.textColor, fontSize: AppSize.sp15)),
// value: clockingBloc.checkBox2.value,
// activeColor: AppColors.primaryColor,
// onChanged: (value) {
// clockingBloc.changeMealValue(context: context, mealValue: value);
// },
// )
//     : Container(),
// isClockedIn
// ? CheckboxListTile(
// contentPadding: EdgeInsets.zero,
// title: Text(AppLocalizations.of(context)!.surChargeService,
// style: context.textTheme.bodyMedium?.copyWith(
// color: context.themeColors.textColor, fontSize: AppSize.sp15)),
// value: clockingBloc.checkBox3.value,
// activeColor: AppColors.primaryColor,
// onChanged: (value) {
// clockingBloc.changeSurchargeValue(
// context: context, surchargeValue: value);
// },
// )
//     : Container(),
// isClockedIn
// ? CheckboxListTile(
// contentPadding: EdgeInsets.zero,
// title: Text(AppLocalizations.of(context)!.workingFromHome,
// style: context.textTheme.bodyMedium?.copyWith(
// color: context.themeColors.textColor, fontSize: AppSize.sp15)),
// value: clockingBloc.checkBox4.value,
// activeColor: AppColors.primaryColor,
// onChanged: (value) {
// clockingBloc.changeWorkFromHomeValue(
// context: context, workFromHomeValue: value);
// },
// )
// : Container(),
