import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_drawer.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_listtile.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/about_app_module/send_feedback_screen/ui/send_feedback_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutTheAppScreen extends StatefulWidget {
  const AboutTheAppScreen({super.key});

  @override
  State<AboutTheAppScreen> createState() => _AboutTheAppScreenState();
}

class _AboutTheAppScreenState extends State<AboutTheAppScreen> {
  String appName = "";
  String packageName = "";
  String version = "";
  String buildNumber = "";
  ValueNotifier<String> appVersion = ValueNotifier("-");

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      version = packageInfo.version;
      buildNumber = packageInfo.buildNumber;
      appVersion.value = "${version}-${buildNumber}";
    });
  }

  Future launchURL(String url) async {
    try {
      log("URL : $url");
      await launchUrl(Uri.parse(url));
    } catch (e) {
      try {
        await launchUrl(Uri.parse("https://staff.nl/privacyverklaring"));
      } catch (e) {
        throw 'Could not launch $url, error: ${e.toString()}';
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.title),
      endDrawer: CommonDrawer(isFromAboutScreen: true),
      body: Padding(
        padding: EdgeInsets.symmetric(
            vertical: AppSize.sp27, horizontal: AppSize.sp14),
        child: Column(
          children: [
            Text(
              AppLocalizations.of(context)!.generalInformationLabel,
              style: context.textTheme.headlineSmall?.copyWith(
                  color: context.themeColors.textColor, fontSize: AppSize.sp14),
            ),
            SpaceV(AppSize.h14),
            Text(
              AppLocalizations.of(context)!.webAppInformationLabel,
              style: context.textTheme.headlineSmall?.copyWith(
                  color: context.themeColors.textColor, fontSize: AppSize.sp14),
            ),
            Spacer(),
            ValueListenableBuilder(
              valueListenable: appVersion,
              builder: (BuildContext context, String value, Widget? child) {
                return CustomListTile(
                  title: AppLocalizations.of(context)!.appVersionLabel,
                  trailingWidget: Text(value),
                  isBorder: true,
                );
              },
            ),
            CustomListTile(
              onTap: () async {
                await launchURL("https://staff.nl/privacyverklaring");
              },
              title: AppLocalizations.of(context)!.privacyPolicyLabel,
              trailingWidget: Icon(Icons.arrow_forward_ios_outlined,
                  size: AppSize.sp18, color: context.themeColors.greyColor),
              isBorder: true,
            ),
            CustomListTile(
              title: AppLocalizations.of(context)!.sendFeedbackButtonLabel,
              trailingWidget: Icon(Icons.arrow_forward_ios_outlined,
                  size: AppSize.sp18, color: context.themeColors.greyColor),
              onTap: () {
                AppNavigation.nextScreen(context, SendFeedBackScreen());
              },
            ),
            // CustomListTile(
            //   onTap: () {
            //     AppNavigation.nextScreen(context, ThirdPartySoftwareScreen());
            //   },
            //   title: AppLocalizations.of(context)!.thirdPartySoftwareLabel,
            //   trailingWidget:
            //       Icon(Icons.arrow_forward_ios_outlined, size: AppSize.sp18, color: context.themeColors.greyColor),
            // ),
          ],
        ),
      ),
    );
  }
}
