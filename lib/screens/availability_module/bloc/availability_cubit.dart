import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/availability_module/model/availability_yearweak_detail_model.dart';
import 'package:staff_medewerker/screens/availability_module/model/availbilty_day_details_model.dart';
import 'package:staff_medewerker/screens/availability_module/repository/availability_repository.dart';

part 'availability_state.dart';

class AvailabilityCubit extends Cubit<AvailabilityState> {
  AvailabilityCubit() : super(AvailabilityInitial());
  final availabilityApiRepository availabilityApi = availabilityApiRepository();

  ValueNotifier<int> initialFetchYear = ValueNotifier(2);
  ValueNotifier<String> shift = ValueNotifier('');
  ValueNotifier<bool> isWeekInfoLoading = ValueNotifier(false);
  ValueNotifier<bool> isWeekMainInfoLoading = ValueNotifier(false);
  ValueNotifier<bool> isWeekDayLoading = ValueNotifier(false);
  ValueNotifier<int> selectedTimeSlotIndex = ValueNotifier(0);
  ValueNotifier<int> selectedTimeUntilSlotIndex = ValueNotifier(0);
  ValueNotifier<Map<int, bool>> weekDaysEditedMap = ValueNotifier({});
  ValueNotifier<List<bool?>> currentYearWeekDaysEditedList = ValueNotifier([]);
  ValueNotifier<List<bool?>> nextYearWeekDaysEditedList = ValueNotifier([]);
  ValueNotifier<bool> isFirstAPICallCompleted = ValueNotifier(true);
  ValueNotifier<bool> isSecondAPICallCompleted = ValueNotifier(false);

  ScrollController scrollController = ScrollController();

  String? remark;
  int? numberOfService;
  String remarkList = '';
  String remarkValue = '';
  bool isFromSelected = false;
  int currentYear = DateTime.now().year;
  int currentWeekNumber = DateTime.now().difference(DateTime(DateTime.now().year, 1, 1)).inDays ~/ 7 + 1;
  // int currentWeekNumber = int.parse('${DateTime.now().year}${Week.current().weekNumber.toString().padLeft(2, '0')}');
  int remainingWeeks = 0;
  int year = 0;
  bool? currentWeekDaysEdited;
  bool? nextWeekDaysEdited;

  AvailabilityYearWeekResponseModel? availableWeekInfo;
  AvailabilityYearWeekResponseModel? availableMainWeekInfo;
  AvailabilityYearWeekResponseModel? availableWeekInfo1;
  List<AvailabilityWeekDayListResponseModel> availabilityDayList = [];
  List<AvailabilityWeekDayListResponseModel> availabilityDayList1 = [];

  List<ValueNotifier<Color>> containerColors =
      List.generate(8, (index) => ValueNotifier<Color>(Color.fromRGBO(46, 76, 112, 1)));
  List<String> timeSlots = [];
  List<String> timeUntilSlots = [];
  List<int> nextWeeks = [];
  List<int> currentYearWeekNumberList = [];
  List<int> nextYearWeekNumberList = [];

  DateTimeRange getISOWeekDateRange(int year, int weekNumber) {
    DateTime januaryFourth = DateTime(year, 1, 4);
    int daysToMonday = 1 - januaryFourth.weekday;
    DateTime startOfWeek = januaryFourth.add(Duration(days: daysToMonday + (weekNumber - 1) * 7));
    DateTime endOfWeek = startOfWeek.add(Duration(days: 6));

    if (endOfWeek.year != year) {
      endOfWeek = DateTime(year, 12, 31);
    }

    return DateTimeRange(start: startOfWeek, end: endOfWeek);
  }

  List<int> generateWeekNumbers(int year, int numberOfWeeks) {
    List<int> weekNumbers = [];
    for (int i = 1; i <= numberOfWeeks; i++) {
      weekNumbers.add(i);
    }
    return weekNumbers;
  }

  Future<void> getWeekShiftInfoData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isWeekInfoLoading.value = true;
    final response = await availabilityApi.getWeekShiftInfoDataApi(context: context, iosYearWeek: iosYearWeek);
    availableWeekInfo = response!;
    availableWeekInfo1 = response;
    log('get shifte data ======>${availableWeekInfo1}');
    isWeekInfoLoading.value = false;
    if (response.statusCode != 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }

  Future<void> getWeekMainShiftInfoData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isWeekMainInfoLoading.value = true;
    final response = await availabilityApi.getWeekShiftInfoDataApi(context: context, iosYearWeek: iosYearWeek);
    isWeekMainInfoLoading.value = false;

    availableMainWeekInfo = response!;

    // log('availableWeekInfo ======>${availableMainWeekInfo?.WeekDaysEdited}');
    // log('availableWeekInfo ======>${availableMainWeekInfo?.EditRight}');
    // log('availableWeekInfo ======>${availableMainWeekInfo?.iSOYearWeek}');

    if (response.statusCode != 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }

  Future<void> settWeekShiftInfoData(
      {required BuildContext context,
      required String iosYearWeek,
      required String numberService,
      required String? remark}) async {
    // isWeekInfoLoading.value = true;
    final response = await availabilityApi.settWeekShiftInfoDataApiCall(
        context: context, iosYearWeek: iosYearWeek, numberService: numberService, remark: remark);
    // isWeekInfoLoading.value = false;
    if (response?.statusCode == 200 && response?.data['Done'] == 1) {
      print('settWeekShiftInfoData =============>${iosYearWeek}');

      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.saved,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );

      fetchAllYearWeekAvailabilityData();
    }
  }

  Future<void> getWeekShiftDetailListInfoData({
    required BuildContext context,
    required String iosYearWeek,
  }) async {
    isWeekDayLoading.value = true;
    final response =
        await availabilityApi.getWeekShiftDetailListInfoDataApi(context: context, iosYearWeek: iosYearWeek);
    availabilityDayList.clear();
    availabilityDayList1.clear();
    availabilityDayList = response!;
    availabilityDayList1 = response.map((item) => new AvailabilityWeekDayListResponseModel.clone(item)).toList();

    isWeekDayLoading.value = false;
    // if(response.statusCode != 200){
    //   customSnackBar(
    //     context: navigatorKey.currentContext!,
    //     message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
    //     actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
    //   );
    // }
  }

  Future<void> settWeekShiftDetailListData(
      {required BuildContext context, required String iosYearWeek, required List dayWeekList}) async {
    //isWeekInfoLoading.value = true;
    final response = await availabilityApi.setWeekShiftDetailListInfoDataApi(
        context: context, iosYearWeek: iosYearWeek, dayWeekList: dayWeekList);
    // isWeekInfoLoading.value = false;
    if (response?.statusCode == 200) {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.saved,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
      print('settWeekShiftInfoData1 =============>');

      fetchAllYearWeekAvailabilityData();
    } else {
      customSnackBar(
        context: navigatorKey.currentContext!,
        message: AppLocalizations.of(navigatorKey.currentContext!)!.errorText,
        actionButtonText: AppLocalizations.of(navigatorKey.currentContext!)!.closeText.toUpperCase(),
      );
    }
  }

  Future<void> fetchAllYearWeekAvailabilityData() async {
    isFirstAPICallCompleted.value = false;
    DateTime now = DateTime.now();
    int currentYear = now.year;

    final futures = <Future<void>>[];

    for (int i = 0; i < 50; i++) {
      int year = currentYear;
      int weekNumber = currentWeekNumber + i;
      if (weekNumber > 52) {
        year++;
        weekNumber -= 52;
      }

      String iosYearWeek = '$year${weekNumber.toString().padLeft(2, '0')}';

      final Future<void> future =
          getWeekMainShiftInfoData(context: navigatorKey.currentContext!, iosYearWeek: iosYearWeek).then((_) {
        weekDaysEditedMap.value[weekNumber] = availableMainWeekInfo!.WeekDaysEdited!;

        log("weekDaysEditedMap.value$weekNumber ${weekDaysEditedMap.value[weekNumber]}");
        log("weekDaysEditedMap.value[weekNumber] ${weekDaysEditedMap.value[weekNumber]}");
      });
      futures.add(future);
    }

    try {
      await Future.wait(futures);
    } catch (e) {
      //print('error s------------>$e');
    }
    isFirstAPICallCompleted.value = true;

    emit(AvailabilityInitial());
  }

  void remarkUpdate(String value) {
    availableWeekInfo?.Remark = value;
    emit(AvailabilityInitial());
  }

  void updateNextYearEditList(bool? value) {
    nextWeekDaysEdited = value;
    emit(AvailabilityInitial());
  }

  void numberOfServicesUpdate(int value) {
    availableWeekInfo?.numberOfServices = value;
    emit(AvailabilityInitial());
  }

  void fromTimeUpdate(String timeSlot) {
    isFromSelected = timeSlots[selectedTimeSlotIndex.value] == timeSlot;
    emit(AvailabilityInitial());
  }

  void fromTimeDisplayUpdate(
    int timeSlot,
    int index,
  ) {
    selectedTimeSlotIndex.value = timeSlot;
    emit(AvailabilityInitial());
  }

  void toTimeDisplayUpdate(
    int timeSlot,
    int index,
  ) {
    selectedTimeUntilSlotIndex.value = timeSlot;
    emit(AvailabilityInitial());
  }

  void fromTimeAndToTimeDisplayUpdate(
    int index,
  ) {
    availabilityDayList[index].FromTime = timeSlots[selectedTimeSlotIndex.value];
    availabilityDayList[index].ToTime = timeUntilSlots[selectedTimeUntilSlotIndex.value];
    emit(AvailabilityInitial());
  }

  remarkListUpdate(int index, String value) {
    availabilityDayList[index].remark = value;
    emit(AvailabilityInitial());
  }
}
