import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

class DatePickerBottomSheet {
  static void selectDateFromCalendar(
      BuildContext context, DeclrationCubit ref, FocusNode focusNode, void Function() onPressed) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows us to define the height
      // backgroundColor: Color.fromRGBO(255, 255, 255, 1),
      // backgroundColor: context.themeColors.declarativeBackgroundColor,

      isDismissible: false,
      shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.only(topLeft: Radius.circular(AppSize.r16), topRight: Radius.circular(AppSize.r16))),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return ValueListenableBuilder(
              valueListenable: ref.selectedDay,
              builder: (context, value, child) {
                return Container(
                  padding: EdgeInsets.all(AppSize.sp16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Close Button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GestureDetector(
                            onTap: () {
                              focusNode.unfocus();

                              Navigator.of(context).pop();
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                color: context.themeColors.declarativeDatePickBackgroundColor,
                                borderRadius: BorderRadius.circular(60),
                              ),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: AppSize.w6,
                                  vertical: AppSize.h2,
                                ),
                                child: Icon(Icons.close),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: AppSize.h10),
                      // Selected Date
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          color: context.themeColors.declarativeBackgroundColor,
                          borderRadius: BorderRadius.circular(AppSize.r12),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: AppSize.h10),
                          child: Center(
                            child: Text(
                              "${DateFormat('dd MMM yyyy').format(value)}",
                              style: Theme.of(context).textTheme.titleLarge,
                            ),
                          ),
                        ),
                      ),
                      // Calendar
                      TableCalendar(
                        firstDay: DateTime(1950),
                        lastDay: DateTime(2100),
                        locale: 'en_US',
                        focusedDay: ref.selectedDay.value,
                        selectedDayPredicate: (day) {
                          return isSameDay(ref.selectedDay.value, day);
                        },
                        onDaySelected: (selected, focused) {
                          ref.selectedDay.value = selected;
                          log('asa ${ref.selectedDay.value}');
                        },
                        headerStyle: HeaderStyle(
                          formatButtonVisible: false,
                          titleCentered: true,
                          titleTextStyle: TextStyle(
                            fontSize: AppSize.sp14,
                            fontWeight: FontWeight.w400,color: context.themeColors.textColor,
                          ),
                        ),
                        calendarBuilders: CalendarBuilders(
                          // outsideBuilder: (context, day, focusedDay) {
                          //   return Container();
                          // },
                          defaultBuilder: (context, day, focusedDay) {
                            return Center(
                              child: Text(
                                '${day.day}',
                                style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13),
                              ),
                            );
                          },
                          todayBuilder: (context, day, focusedDay) {
                            return Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: context.themeColors.primaryColor,
                                ),
                              ),
                              child: Center(
                                child: Text(
                                  '${day.day}',
                                  style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13),
                                ),
                              ),
                            );
                          },
                          selectedBuilder: (context, day, focusedDay) {
                            return Container(
                              decoration: BoxDecoration(
                                color: context.themeColors.primaryColor,
                                borderRadius: BorderRadius.circular(AppSize.r10),
                              ),
                              child: Center(
                                child: Text(
                                  '${day.day}',
                                  style: context.textTheme.titleMedium?.copyWith(
                                    fontSize: AppSize.sp13,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      SpaceV(AppSize.h10),
                      // Button
                      Padding(
                        padding: EdgeInsets.only(left: AppSize.w12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                focusNode.unfocus();

                                Navigator.pop(context);
                              },
                              child: Text(
                                AppLocalizations.of(context)!.cancelText,
                                style: context.textTheme.titleMedium
                                    ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.primaryColor),
                              ),
                            ),
                            ReusableContainerButton(
                              elevation: 0,
                              height: AppSize.h36,
                              width: AppSize.w100,
                              borderRadius: BorderRadius.circular(AppSize.r24),
                              onPressed: onPressed,
                              buttonText: AppLocalizations.of(context)!.confirm,
                              textStyle: context.textTheme.bodySmall?.copyWith(
                                color: Colors.white,
                                fontSize: AppSize.sp13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      },
    );
  }

  static void confirmVoucherSheet(
      BuildContext context, FocusNode focusNode, FocusNode focusNode1, void Function() onPressed) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allows us to define the height
      // backgroundColor: Color.fromRGBO(255, 255, 255, 1),
      backgroundColor: context.themeColors.declarativeBackgroundColor,

      isDismissible: false,
      shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.only(topLeft: Radius.circular(AppSize.r16), topRight: Radius.circular(AppSize.r16))),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Container(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: AppSize.h10, horizontal: AppSize.w20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SpaceV(AppSize.h12),
                    Text(AppLocalizations.of(context)!.confirmDeclaration,
                        style: context.textTheme.headlineLarge?.copyWith()),
                    SpaceV(AppSize.h14),
                    Text(AppLocalizations.of(context)!.notundoneDeclration,
                        style: context.textTheme.bodySmall?.copyWith(color: context.themeColors.greyColor)),
                    SpaceV(AppSize.h16),
                    Padding(
                      padding: EdgeInsets.only(left: AppSize.w12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              focusNode.unfocus();
                              focusNode1.unfocus();

                              Navigator.pop(context);
                            },
                            child: Text(
                              AppLocalizations.of(context)!.goBack,
                              style: context.textTheme.titleMedium
                                  ?.copyWith(fontSize: AppSize.sp13, color: context.themeColors.primaryColor),
                            ),
                          ),
                          ReusableContainerButton(
                            elevation: 0,
                            height: AppSize.h36,
                            width: AppSize.w100,
                            borderRadius: BorderRadius.circular(AppSize.r24),
                            onPressed: onPressed,
                            buttonText: AppLocalizations.of(context)!.declrationSave,
                            textStyle: context.textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontSize: AppSize.sp13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SpaceV(AppSize.h16),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
