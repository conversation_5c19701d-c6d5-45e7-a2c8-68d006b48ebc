// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/mileage_register_screen.dart';
import 'package:staff_medewerker/screens/declaration_module/ui/voucher_register_screen.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';

class VoucherPhotoUploadScreen extends StatelessWidget {
  final int index;
  final String guid;
  const VoucherPhotoUploadScreen({
    Key? key,
    required this.index,
    required this.guid,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeclrationCubit, DeclrationState>(
      builder: (ctx, state) {
        final ref = ctx.read<DeclrationCubit>();

        return Scaffold(
          //   backgroundColor: Color.fromRGBO(239, 239, 239, 1),
          appBar: CustomAppBar(
              title: index == 1
                  ? AppLocalizations.of(context)!.mileageRegistration
                  : AppLocalizations.of(context)!.submitVoucher),
          body: Padding(
            padding: EdgeInsets.all(AppSize.sp20),
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              //   color: Color.fromRGBO(255, 253, 253, 1),
              color: context.themeColors.homeContainerColor,

              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    AssetsPath.uloadIcon,
                    height: AppSize.w120,
                    width: AppSize.w120,
                  ),
                  SpaceV(AppSize.h40),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: AppSize.w100),
                    child: ReusableContainerButton(
                        elevation: 0,
                        height: AppSize.h36,
                        borderRadius: BorderRadius.circular(AppSize.r24),
                        onPressed: () async {
                          String imageBase64 = await ref.pickImage(profileImage: ImageSource.camera, context: context);
                          if (imageBase64.isNotEmpty) {
                            index == 1
                                ? AppNavigation.nextScreen(
                                    context,
                                    BlocProvider.value(
                                        value: ref,
                                        child: MileageRegisterScreen(
                                          guid: guid,
                                        )))
                                : AppNavigation.nextScreen(
                                    context,
                                    BlocProvider.value(
                                        value: ref,
                                        child: VoucherRegisterScreen(
                                          guid: guid,
                                        )));
                          } else {
                            print("Image picking failed or no image selected");
                          }
                        },
                        buttonText: AppLocalizations.of(context)!.takePhoto,
                        textStyle: context.textTheme.bodySmall
                            ?.copyWith(color: Colors.white, fontSize: AppSize.sp13, fontWeight: FontWeight.w500)),
                  ),
                  SpaceV(AppSize.h20),
                  GestureDetector(
                    onTap: () async {
                      String imageBase64 = await ref.pickImage(profileImage: ImageSource.gallery, context: context);
                      if (imageBase64.isNotEmpty) {
                        index == 1
                            ? AppNavigation.nextScreen(
                                context, BlocProvider.value(value: ref, child: MileageRegisterScreen(guid: guid)))
                            : AppNavigation.nextScreen(
                                context, BlocProvider.value(value: ref, child: VoucherRegisterScreen(guid: guid)));
                      } else {
                        print("Image picking failed or no image selected");
                      }
                    },
                    child: Text(AppLocalizations.of(context)!.browsingPhone,
                        style: context.textTheme.bodyLarge
                            ?.copyWith(color: context.themeColors.primaryColor, fontSize: AppSize.sp13)),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
