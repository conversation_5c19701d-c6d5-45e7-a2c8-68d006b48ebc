import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/common_button.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/declaration_module/cubit/declration_cubit.dart';
import 'package:staff_medewerker/screens/declaration_module/widgets/custom_delcaration_textfield.dart';
import 'package:staff_medewerker/screens/declaration_module/widgets/select_date_picker_bottom_sheet.dart';
import 'package:staff_medewerker/utils/appsize.dart';

import '../../../main.dart';

class VoucherRegisterScreen extends StatefulWidget {
  final String guid;
  VoucherRegisterScreen({
    super.key,
    required this.guid,
  });

  @override
  State<VoucherRegisterScreen> createState() => _VoucherRegisterScreenState();
}

class _VoucherRegisterScreenState extends State<VoucherRegisterScreen> {
  FocusNode _focusNode = FocusNode();

  FocusNode _focusNode1 = FocusNode();

  final GlobalKey<FormState> _formKey = GlobalKey();

  @override
  void initState() {
    final blocProvider = BlocProvider.of<DeclrationCubit>(context);
    blocProvider.clearData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DeclrationCubit, DeclrationState>(
      builder: (ctx, state) {
        final ref = ctx.read<DeclrationCubit>();

        if (state is DeclrationUdateDate) log('confirmDate ${state.confirmDate}');
        return Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: context.themeColors.declarativeScaffoldBackgroundColor,
          appBar: CustomAppBar(title: AppLocalizations.of(context)!.submitVoucher),
          body: GestureDetector(
            onTap: () {
              FocusManager.instance.primaryFocus?.unfocus();
            },
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(AppSize.sp20),
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height,
                    color: context.themeColors.declarativeBackgroundColor,

                    // color: Color.fromRGBO(255, 253, 253, 1),
                    //    co
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w20, vertical: AppSize.h10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        // mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpaceV(AppSize.h30),
                          Text(AppLocalizations.of(context)!.newReceipt,
                              style: context.textTheme.titleMedium?.copyWith()),
                          SpaceV(AppSize.h20),
                          Text(AppLocalizations.of(context)!.nameBean,
                              style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13)),
                          SpaceV(AppSize.h10),
                          CustomDelcarationTextfield(
                            controller: ref.nameController,
                            labelText: AppLocalizations.of(context)!.bon,
                            validateMsg: AppLocalizations.of(context)!.enterNameofBon,
                          ),
                          SpaceV(AppSize.h20),
                          Text(AppLocalizations.of(context)!.receiptSummary,
                              style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13)),
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            // keyboardType: TextInputType.number,
                            keyboardType: TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              ReplaceCommaFormatter(),
                            ],
                            controller: ref.totalAmountController,
                            labelText: AppLocalizations.of(context)!.totalAmount,
                            validateMsg: AppLocalizations.of(context)!.enterTotalAmount,
                          ),
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            controller: ref.dateController,
                            labelText: AppLocalizations.of(context)!.date,
                            readOnly: true,
                            validateMsg: AppLocalizations.of(context)!.selectDate,
                            onTap: () {
                              DatePickerBottomSheet.selectDateFromCalendar(context, ref, _focusNode, () {
                                ref.updateDate(ref.selectedDay.value);
                                log("sssssssssssssselectedDate:${ref.selectedDay.value}");
                                ref.dateController.text =
                                    DateFormat('dd MMM yyyy').format(ref.selectedDay.value).toString();
                                debugPrint('${(state is DeclrationUdateDate) ? state.confirmDate : null}');
                                Navigator.pop(context);
                              });
                            },
                          ),
                          /*
                          InkWell(
                            child: GestureDetector(
                              onTap: () {
                                _focusNode.unfocus();
                                DatePickerBottomSheet.selectDateFromCalendar(context, ref, _focusNode, () {
                                  _focusNode.unfocus();
                                  ref.updateDate(ref.selectedDay.value);
                                  debugPrint('${(state is DeclrationUdateDate) ? state.confirmDate : null}');
                                  Navigator.pop(context);
                                });
                              },
                              child: Container(
                                height: AppSize.h40,
                                width: MediaQuery.of(context).size.width,
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(
                                      AppSize.r4,
                                    ),
                                    border: Border.all(
                                      color: context.themeColors.greyColor,
                                    )),
                                child: Row(
                                  children: [
                                    Padding(
                                      padding: EdgeInsets.only(left: AppSize.w8),
                                      child: Center(
                                        child: Text(
                                          (state is DeclrationUdateDate)
                                              ? DateFormat('dd MMM yyyy').format(state
                                                  .confirmDate) // If state is DeclrationUdateDate, format and show the confirmDate
                                              : AppLocalizations.of(context)!.receiptDate,
                                          style: context.textTheme.bodySmall
                                              ?.copyWith(color: context.themeColors.greyColor, fontSize: AppSize.sp13),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
            */
                          SpaceV(AppSize.h14),
                          CustomDelcarationTextfield(
                            maxLines: 2,
                            controller: ref.descriptionController,
                            focusNode: _focusNode1,
                            labelText: AppLocalizations.of(context)!.description,
                            validateMsg: AppLocalizations.of(context)!.enterDescription,
                          ),
                          SpaceV(AppSize.h30),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: AppSize.w70),
                            child: ReusableContainerButton(
                                elevation: 0,
                                height: AppSize.h36,
                                borderRadius: BorderRadius.circular(AppSize.r24),
                                onPressed: () {
                                  _focusNode.unfocus();
                                  _focusNode1.unfocus();
                                  var form = _formKey.currentState!;
                                  if (form.validate()) {
                                    form.save();
                                    DatePickerBottomSheet.confirmVoucherSheet(context, _focusNode, _focusNode1, () {
                                      _focusNode.unfocus();
                                      _focusNode1.unfocus();

                                      Map<String, dynamic> bodyData = {
                                        "APIKeyLogin": {"DeviceId": "", "APIKey": APIKey},
                                        "Date": ref.selectedDay.value.toString(),
                                        "TypeId": widget.guid,
                                        "FileName": ref.imageName.value.toString(),
                                        "FileSize": ref.imageBaseString.length,
                                        "Attachment": ref.imageBaseString,
                                        "MimeType": ref.imageName.value.toString(),
                                        "Bon": ref.nameController.text.toString(),
                                        "Costs": ref.totalAmountController.text.toString(),
                                        "Description": ref.descriptionController.text.toString(),
                                        "ProjectTaskId": widget.guid,
                                      };
                                      log('data ====>${bodyData}');
                                      ref.getCreateDeclarationData(context: context, data: bodyData);
                                    });
                                  } else {
                                    return null;
                                  }
                                },
                                buttonText: AppLocalizations.of(context)!.saveReceipt,
                                textStyle: context.textTheme.bodySmall?.copyWith(
                                    color: Colors.white, fontSize: AppSize.sp13, fontWeight: FontWeight.w500)),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class ReplaceCommaFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.replaceAll(',', '.'),
      selection: newValue.selection,
    );
  }
}
