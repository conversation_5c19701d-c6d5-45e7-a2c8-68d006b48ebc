import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/open_service_module/bloc/open_service_cubit.dart';
import 'package:staff_medewerker/screens/open_service_module/ui/week_details_widget.dart';
import 'package:staff_medewerker/screens/open_service_module/widgets/shimmer_widget.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

class OpenServicesScreen extends StatefulWidget {
  OpenServicesScreen({Key? key}) : super(key: key);

  @override
  State<OpenServicesScreen> createState() => _OpenServicesScreenState();
}

class _OpenServicesScreenState extends State<OpenServicesScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await BlocProvider.of<OpenServiceCubit>(context).fetchMonthData(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.openServices),
      body: BlocBuilder<OpenServiceCubit, OpenServiceState>(
        builder: (ctx, state) {
          final scheduleBloc = ctx.read<OpenServiceCubit>();

          return RefreshIndicator(
            onRefresh: () {
              return BlocProvider.of<OpenServiceCubit>(context).fetchMonthData(context);
            },
            child: Container(
              color: context.themeColors.homeContainerColor,
              child: Column(
                children: [
                  Expanded(
                      child: ValueListenableBuilder(
                    valueListenable: scheduleBloc.isOpenServiceMonthLoading,
                    builder: (context, isScheduleMonthLoading, child) {
                      if (!isScheduleMonthLoading) {
                        if (scheduleBloc.myServicePerMonthList.isNotEmpty) {
                          return ListView.builder(
                            itemCount: scheduleBloc.myServicePerMonthList.length,
                            itemBuilder: (context, index) {
                              DateTime currentISODate =
                                  DateTime.parse(scheduleBloc.myServicePerMonthList[index].iSODate.toString());
                              bool isDifferentDate = index == 0 ||
                                  currentISODate !=
                                      DateTime.parse(scheduleBloc.myServicePerMonthList[index - 1].iSODate.toString());

                              if (isDifferentDate) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Container(
                                      color: context.themeColors.listWeekGridColor,
                                      height: AppSize.h30,
                                      padding: EdgeInsets.only(left: AppSize.sp14),
                                      alignment: Alignment.centerLeft,
                                      child: Text(
                                        DateFormat('EEEE d MMMM', appDB.language).format(currentISODate),
                                        style: context.textTheme.bodyMedium?.copyWith(
                                          fontSize: AppSize.sp15,
                                          color: context.themeColors.textColor,
                                        ),
                                      ),
                                    ),
                                    MonthDetailWidget(
                                      index: index,
                                    ),
                                  ],
                                );
                              } else {
                                return MonthDetailWidget(
                                  index: index,
                                );
                              }
                            },
                          );
                        } else {
                          return Center(
                            child: Text(AppLocalizations.of(context)!.noShifts,
                                style: context.textTheme.bodyMedium
                                    ?.copyWith(fontSize: AppSize.sp15, color: context.themeColors.textColor)),
                          );
                        }
                      } else {
                        return OpenServiceMonthShimmerWidget();
                      }
                    },
                  ))
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
