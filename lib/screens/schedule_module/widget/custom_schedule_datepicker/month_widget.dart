import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/absence_module/ui/widgets/custom_datepicker/datepicker_cubit.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_date_picker_cubit.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';

class ScheduleMonthPicker extends StatelessWidget {
  final List<String> monthNames = [
    // 'January',
    // 'February',
    // 'March',
    // 'April',
    // 'May',
    // 'June',
    // 'July',
    // 'August',
    // 'September',
    // 'October',
    // 'November',
    // 'December'
    AppLocalizations.of(navigatorKey.currentContext!)!.january,
    AppLocalizations.of(navigatorKey.currentContext!)!.february,
    AppLocalizations.of(navigatorKey.currentContext!)!.march,
    AppLocalizations.of(navigatorKey.currentContext!)!.april,
    AppLocalizations.of(navigatorKey.currentContext!)!.may,
    AppLocalizations.of(navigatorKey.currentContext!)!.june,
    AppLocalizations.of(navigatorKey.currentContext!)!.july,
    AppLocalizations.of(navigatorKey.currentContext!)!.august,
    AppLocalizations.of(navigatorKey.currentContext!)!.september,
    AppLocalizations.of(navigatorKey.currentContext!)!.october,
    AppLocalizations.of(navigatorKey.currentContext!)!.november,
    AppLocalizations.of(navigatorKey.currentContext!)!.december
  ];

  @override
  Widget build(BuildContext context) {
    // int currentMonth = DateTime.now().month;

    DateFormat format = DateFormat("dd MMMM yyyy");

    int currentMonth = format.parse(BlocProvider.of<ScheduleTimeCubit>(context).selectedDateString.value).month;
    context.read<DateCubit>().setMonth(currentMonth);
    return BlocBuilder<DateCubit, DateTime>(
      builder: (context, date) {
        return ListWheelScrollView(
          itemExtent: 40,
          perspective: 0.**********,
          useMagnifier: false,
          physics: FixedExtentScrollPhysics(),
          onSelectedItemChanged: (index) {
            context.read<DateCubit>().setMonth(index + 1);
          },
          children: List<Widget>.generate(12, (index) {
            final value = index + 1;
            final isSelected = (value == date.month);
            return Text(monthNames[index], // Use month names
                style: context.textTheme.bodyMedium?.copyWith(
                  fontSize: isSelected ? AppSize.sp18 : AppSize.sp16,
                  color: isSelected ? AppColors.primaryColor : context.themeColors.textColor,
                ));
          }),
          controller: FixedExtentScrollController(initialItem: currentMonth - 1),
        );
      },
    );
  }
}
