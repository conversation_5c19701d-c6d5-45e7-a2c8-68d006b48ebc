import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/screens/schedule_module/bloc/schedule_cubit.dart';

import '../../../../common/custom_widgets/custom_switch.dart';
import '../../../../common/custom_widgets/spacebox.dart';
import '../../../../utils/appsize.dart';
import '../../../../utils/colors/app_colors.dart';
import '../../../hours_module/widget/day_activity_container.dart';
import '../../widget/schedule_shimmer_day.dart';
import '../../widget/schedule_type_popup.dart';

class ScheduleDayScreen extends StatefulWidget {
  const ScheduleDayScreen({Key? key}) : super(key: key);

  @override
  State<ScheduleDayScreen> createState() => _ScheduleDayScreenState();
}

class _ScheduleDayScreenState extends State<ScheduleDayScreen> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    final scheduleCubit = BlocProvider.of<ScheduleCubit>(context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await scheduleCubit.fetchDepartmentAndDayData(firstTime: true);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: BlocBuilder<ScheduleCubit, ScheduleState>(
        builder: (ctx, state) {
          final scheduleBloc = ctx.read<ScheduleCubit>();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSize.w12,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () async {
                        scheduleBloc.selectDayValue.value = scheduleBloc.selectDayValue1.value;
                        await showDialog(
                          context: context,
                          builder: (context) {
                            return scheduleTypeShowDialog(
                              dialogTitle: AppLocalizations.of(context)!.department,
                              context: context,
                              list: scheduleBloc.titleList.isNotEmpty
                                  ? scheduleBloc.titleList
                                  : [AppLocalizations.of(context)!.noDepartment],
                              selectedValue:
                                  scheduleBloc.titleList.isNotEmpty ? scheduleBloc.selectDayValue : ValueNotifier(1),
                              // selectedValue: scheduleBloc.selectDayValue,
                              onChanged: (val) async {
                                scheduleBloc.selectDayValue.value = val!;
                                print("=============>${scheduleBloc.guids[scheduleBloc.selectDayValue.value - 1]}");
                              },
                              onOKPressed: (String) async {
                                scheduleBloc.selectDayValue1.value = String;
                                if (scheduleBloc.titleList.isNotEmpty) {
                                  // print(
                                  //     "selectedGuid =============>${scheduleBloc.guids[scheduleBloc.selectDayValue.value - 1]}");
                                  // print("scheduleBloc.titleList =============>${scheduleBloc.titleList}");
                                  scheduleBloc.selectedGuid = scheduleBloc.guids[scheduleBloc.selectDayValue.value - 1];

                                  if (String >= 1 && String <= scheduleBloc.titleList.length) {
                                    scheduleBloc.selectedText = scheduleBloc.titleList[String - 1];
                                  } else {
                                    scheduleBloc.selectedText = scheduleBloc.titleList.isNotEmpty
                                        ? scheduleBloc.titleList[0]
                                        : AppLocalizations.of(context)!.noDepartment;
                                  }

                                  // var department = scheduleBloc.scheduleDepartmentList
                                  //     .firstWhere((dept) => dept.title == scheduleBloc.selectedText);

                                  // Check if a department with the specified title was found
                                  // Access the guid
                                  // scheduleBloc.selectedGuid = department.guid;
                                  // print("Department Guid for ${department.title}: ${scheduleBloc.selectedGuid}");
                                  BlocProvider.of<ScheduleCubit>(context)
                                      .fetchDayData(context, guid: scheduleBloc.selectedGuid ?? '');
                                  await BlocProvider.of<ScheduleCubit>(context).fetchDepartmentAndDayData();
                                  print("selectedGuid =============>${scheduleBloc.selectedGuid}");
                                  // setState(() {});
                                } else {}
                              },
                            );
                          },
                        );
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.department,
                            style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp12),
                          ),
                          Row(
                            children: [
                              ValueListenableBuilder(
                                valueListenable: scheduleBloc.selectDayValue1,
                                builder: (context, value, child) {
                                  log('=========>${scheduleBloc.selectedText}');
                                  log('=========>${scheduleBloc.titleList}');

                                  return Text(
                                      scheduleBloc.titleList.isNotEmpty
                                          ? (scheduleBloc.titleList.contains(scheduleBloc.selectedText)
                                              ? (scheduleBloc.selectedText ??
                                                  AppLocalizations.of(context)!.noDepartment)
                                              : scheduleBloc.titleList.first ?? '')
                                          : AppLocalizations.of(context)!.noDepartment,
                                      style: context.textTheme.bodyMedium?.copyWith(
                                          color: context.themeColors.textColor, fontSize: AppSize.sp15, height: 1.3));
                                },
                              ),
                              SpaceH(AppSize.w4),
                              Icon(
                                Ionicons.caret_down_outline,
                                size: AppSize.sp12,
                                color: AppColors.greyColor,
                              )
                            ],
                          )
                        ],
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          'Details tonen',
                          style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13),
                        ),
                        SpaceH(AppSize.w10),
                        CustomSwitch(
                          value: scheduleBloc.isDetail,
                          inactiveTrackColor: AppColors.darkModeGreyColor,
                          onChanged: (value) {
                            scheduleBloc.updateDetailValue(
                              !scheduleBloc.isDetail,
                            );
                          },
                        )
                      ],
                    ),
                  ],
                ),
              ),
              SpaceV(AppSize.h4),
              Divider(
                color: context.themeColors.dividerAvailbilityColor,
                height: 0,
                thickness: 1.2,
              ),
              SpaceV(AppSize.h10),
              Expanded(
                  child: ValueListenableBuilder(
                valueListenable: scheduleBloc.isDateLoading,
                builder: (context, value, child) {
                  if (!scheduleBloc.isDateLoading.value) {
                    // print('------------------>${scheduleBloc.scheduleDateList}');
                    if (scheduleBloc.scheduleDateList.isNotEmpty) {
                      return ListView.builder(
                        itemBuilder: (context, index) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: AppSize.w4, vertical: index % 2 != 0 ? AppSize.h12 : 0),
                                decoration: BoxDecoration(
                                    color: index % 2 != 0
                                        ? context.themeColors.calendarSecondIndexColor
                                        : Colors.transparent),
                                child: Row(
                                  children: [
                                    Text(
                                      '${scheduleBloc.scheduleDateList[index].timeFrom.toString()} - ${scheduleBloc.scheduleDateList[index].timeUntil.toString()}',
                                      style: context.textTheme.titleMedium
                                          ?.copyWith(fontSize: AppSize.sp13, fontWeight: FontWeight.w900),
                                    ),
                                    SpaceH(AppSize.w8),
                                    Expanded(
                                      child: Text(
                                        "${scheduleBloc.scheduleDateList[index].employee.toString()}",
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: context.textTheme.titleMedium?.copyWith(fontSize: AppSize.sp13),
                                      ),
                                    ),
                                    Text(
                                      '${scheduleBloc.scheduleDateList[index].department.toString()}',
                                      style: context.textTheme.titleMedium?.copyWith(
                                        fontSize: AppSize.sp13,
                                        // overflow: TextOverflow.ellipsis
                                      ),
                                    ),
                                    SpaceH(AppSize.w4)
                                  ],
                                ),
                              ),
                              scheduleBloc.isDetail ? SpaceV(0) : SpaceV(AppSize.h12),
                              (scheduleBloc.isDetail)
                                  ? Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: index % 2 != 0 ? 0 : AppSize.h10, horizontal: AppSize.w6),
                                          decoration: BoxDecoration(
                                              color: index % 2 != 0
                                                  ? context.themeColors.calendarSecondIndexColor
                                                  : Colors.transparent),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  DayActivityContainer(
                                                      titleText: AppLocalizations.of(context)!.activityText,
                                                      valueText: scheduleBloc.scheduleDateList[index].costCenters),
                                                  DayActivityContainer(
                                                      titleText: AppLocalizations.of(context)!.shifts,
                                                      valueText: scheduleBloc.scheduleDateList[index].service),
                                                  (scheduleBloc.scheduleDateList[index].calendarEntry!.length >= 4 &&
                                                          scheduleBloc
                                                              .scheduleDateList[index].calendarEntry!.isNotEmpty)
                                                      ? Expanded(
                                                          child: DayActivityContainer(
                                                              titleText: AppLocalizations.of(context)!.assignment,
                                                              valueText:
                                                                  scheduleBloc.scheduleDateList[index].calendarEntry),
                                                        )
                                                      : DayActivityContainer(
                                                          titleText: AppLocalizations.of(context)!.assignment,
                                                          valueText: '-'),
                                                  DayActivityContainer(
                                                      titleText: AppLocalizations.of(context)!.rosterGroup,
                                                      valueText: '-')
                                                ],
                                              ),
                                              SpaceV(AppSize.h10),
                                              DayActivityContainer(
                                                  titleText: AppLocalizations.of(context)!.remark,
                                                  valueText: scheduleBloc.scheduleDateList[index].remark),
                                            ],
                                          ),
                                        ),
                                        SpaceV(AppSize.h10),
                                      ],
                                    )
                                  : SizedBox()
                            ],
                          );
                        },
                        itemCount: scheduleBloc.scheduleDateList.length,
                      );
                    } else {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: AppSize.w12),
                        child: Text(AppLocalizations.of(context)!.noScheduleAvailable,
                            style: context.textTheme.bodyMedium
                                ?.copyWith(color: context.themeColors.textColor, fontSize: AppSize.sp15, height: 1.3)),
                      );
                    }
                  } else {
                    return ScheduleDayShimmerWidget();
                  }
                },
              ))
            ],
          );
        },
      ),
    );
  }
}
