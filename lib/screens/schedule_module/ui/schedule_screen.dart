import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/appbar_custom.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/calendar_screens/schedule_calendar_screen.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/day_screens/schedule_day_screen.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/month_screens/schedule_month_screen.dart';
import 'package:staff_medewerker/screens/schedule_module/ui/week_screens/schedule_week_screen.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../common/custom_date_picker/date_picker.dart';
import '../../../common/custom_widgets/spacebox.dart';
import '../bloc/schedule_cubit.dart';
import '../bloc/schedule_date_picker_cubit.dart';
import '../widget/application_reference_popup.dart';
import '../widget/custom_schedule_datepicker/date_picker.dart';

class ScheduleScreen extends StatefulWidget {
  final bool isScheduleScreen;

  const ScheduleScreen({super.key, this.isScheduleScreen = true});

  @override
  State<ScheduleScreen> createState() => _ScheduleScreenState();
}

class _ScheduleScreenState extends State<ScheduleScreen> {
  ValueNotifier<int> selectValue = ValueNotifier(1);
  String selectedDate = '';
  DateTime originalDate = DateTime.now();
  List<String> scheduleList = [
    AppLocalizations.of(navigatorKey.currentContext!)!.day,
    AppLocalizations.of(navigatorKey.currentContext!)!.week,
    AppLocalizations.of(navigatorKey.currentContext!)!.month,
    AppLocalizations.of(navigatorKey.currentContext!)!.calendar,
  ];

  @override
  void initState() {
    super.initState();
    final scheduleBloc = BlocProvider.of<ScheduleCubit>(context, listen: false);
    scheduleBloc.selectedTitles.value = scheduleBloc.applicationReferenceList1.value
        .where((item) => item.isSelected)
        .map((item) => item.title)
        .join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final scheduleDayBloc = BlocProvider.of<ScheduleTimeCubit>(navigatorKey.currentContext!);
    final scheduleBloc = BlocProvider.of<ScheduleCubit>(context, listen: false);
    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
        isLeading: true,
        leadingWidth: AppSize.w240,
        leading: Padding(
          padding: EdgeInsets.only(left: AppSize.w12),
          child: BlocBuilder<ScheduleTimeCubit, bool>(
            builder: (ctx, state) {
              return ValueListenableBuilder(
                valueListenable: scheduleDayBloc.selectedDateString,
                builder: (context, value, child) {
                  DateTime originalDate = DateFormat("dd MMMM yyyy").parse(value);

                  //calculation for week
                  DateTime getDate(DateTime d) => DateTime(d.year, d.month, d.day);
                  DateTime firstDayOfWeek = getDate(originalDate.subtract(Duration(days: originalDate.weekday - 1)));
                  DateTime lastDayOfWeek =
                      getDate(originalDate.add(Duration(days: DateTime.daysPerWeek - originalDate.weekday)));
                  log('Start of week: ${getDate(originalDate.subtract(Duration(days: originalDate.weekday - 1)))}');
                  log('End of week: ${getDate(originalDate.add(Duration(days: DateTime.daysPerWeek - originalDate.weekday)))}');
                  scheduleBloc.formattedWeek =
                      '${DateFormat('MMM d').format(firstDayOfWeek)} - ${DateFormat('d, y').format(lastDayOfWeek)}';
                  log('End of week: ${scheduleBloc.formattedWeek}');

                  //calculation for month
                  scheduleBloc.formattedMonthDate = DateFormat("MMMM yyyy").format(originalDate);

                  //calculation for day
                  scheduleBloc.formattedDay = DateFormat('MMMM d, y').format(originalDate);

                  return ValueListenableBuilder(
                    valueListenable: selectValue,
                    builder: (BuildContext context, value, Widget? child) {
                      return InkWell(
                        onTap: () async {
                          if (selectValue.value == 3 || selectValue.value == 4) {
                            showDialog(
                              context: context,
                              builder: (context) {
                                return AlertDialog(
                                  backgroundColor: context.themeColors.drawerColor,
                                  content: Container(
                                    width: MediaQuery.of(context).size.width * 0.6, // Set the desired width here
                                    child: ScheduleDatePickerWidget(onOkPressed: (DateTime selectedValue) async {
                                      ctx.read<ScheduleTimeCubit>().setDate(selectedValue: selectedValue);
                                      scheduleBloc.selectDate = selectedValue;
                                      await scheduleBloc.apiCall(context, selectValue.value);
                                    }),
                                  ),
                                );
                              },
                            );
                          } else {
                            final selectedValue = await CustomDatePicker(
                              context: context,
                              locale: Locale(appDB.language ?? 'en'),
                              initialDate: scheduleBloc.selectDate,
                              firstDate: DateTime(DateTime.now().year - 100),
                              lastDate: DateTime(DateTime.now().year + 1),
                              builder: (context, child) {
                                return Theme(
                                  data: ThemeData.light().copyWith(
                                      colorScheme: ColorScheme.dark(
                                          onPrimary: AppColors.white, // selected text color
                                          onSurface: context.themeColors.textColor, // default date text color
                                          primary: AppColors.primaryColor, // circle color
                                          surface: context.themeColors.drawerColor // background color,
                                          ),
                                      dialogBackgroundColor: context.themeColors.listGridColor1,
                                      textButtonTheme: TextButtonThemeData(
                                          style: TextButton.styleFrom(foregroundColor: AppColors.primaryColor))),
                                  child: child!,
                                );
                              },
                            );

                            if (selectedValue != null) {
                              if (selectedValue != scheduleBloc.selectDate) {
                                ctx.read<ScheduleTimeCubit>().setDate(selectedValue: selectedValue);
                                log("Selected date is: $selectedValue");
                                scheduleBloc.selectDate = selectedValue;
                                await scheduleBloc.apiCall(context, selectValue.value);

                                print("Selected date is: $selectedValue");
                              } else {}
                            } else {}
                          }
                        },
                        child: Row(
                          children: [
                            BlocBuilder<ScheduleCubit, ScheduleState>(
                              builder: (ctx1, state) {
                                return Text(
                                    selectValue.value == 1
                                        ? scheduleBloc.formattedDay
                                        : selectValue.value == 2
                                            ? scheduleBloc.formattedWeek
                                            : selectValue.value == 3
                                                ? scheduleBloc.formattedMonthDate
                                                : scheduleBloc.formattedMonthDate,
                                    style: context.textTheme.headlineLarge!.copyWith(
                                        color: Colors.white, fontSize: AppSize.sp17, fontWeight: FontWeight.w500));
                              },
                            ),
                            Icon(Icons.arrow_drop_down_rounded, size: AppSize.sp30)
                          ],
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
        actions: true,
        actionList: [
          PopupMenuButton(
            offset: Offset(0.0, AppBar().preferredSize.height * 0.9),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSize.r4),
            ),
            color: context.themeColors.listGridColor1,
            icon: Icon(Icons.more_vert_rounded, color: AppColors.white),
            itemBuilder: (context) {
              return <PopupMenuEntry<int>>[
                PopupMenuItem<int>(
                  onTap: () async {
                    await BlocProvider.of<ScheduleCubit>(context).scheduleCalendarMonthData(
                      context: context,
                    );
                  },
                  padding: EdgeInsets.only(right: AppSize.w100, left: AppSize.w12),
                  value: 0,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(AppLocalizations.of(context)!.download,
                          textAlign: TextAlign.center,
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: context.themeColors.textColor,
                            fontSize: AppSize.sp15,
                          )),
                      Icon(
                        Ionicons.download,
                        color: context.themeColors.iconColor,
                      )
                    ],
                  ),
                ),
              ];
            },
          ),
        ],
        title: '',
      ),
      body: RefreshIndicator(
        color: AppColors.primaryColor,
        onRefresh: () {
          return scheduleBloc.apiCall(context, selectValue.value);
        },
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(
                  left: AppSize.w16,
                  right: AppSize.w16,
                  top: AppSize.h16,
                  bottom: selectValue.value == 4 ? 0 : AppSize.h16),
              child: Column(
                children: [
                  Container(
                    height: AppSize.h32,
                    width: MediaQuery.of(context).size.width,
                    decoration: BoxDecoration(
                      color: context.themeColors.scheduleContainerColor,
                      borderRadius: BorderRadius.circular(AppSize.r8),
                    ),
                    child: Row(
                      children: [
                        for (int index = 0; index < scheduleList.length; index++)
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                selectValue.value = index + 1;
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    // borderRadius: BorderRadius.circular(AppSize.r8),
                                    // border: Border.all(color: Colors.grey), // Customize the border color and width
                                    ),
                                child: Padding(
                                  padding: EdgeInsets.only(
                                      top: AppSize.h3,
                                      bottom: AppSize.h3,
                                      left: AppSize.w3,
                                      right: AppSize.w3), // Add padding to the items
                                  child: ValueListenableBuilder(
                                    valueListenable: selectValue,
                                    builder: (context, value, child) {
                                      final isSelected = index == value - 1;
                                      return Container(
                                        decoration: BoxDecoration(
                                          color: isSelected ? context.themeColors.scheduleSelectedContainerColor : null,

                                          border: (!isSelected && (index < scheduleList.length - 1))
                                              ? Border(
                                                  right: BorderSide(
                                                      color: context.themeColors.scheduleDividerColor, width: 1.5))
                                              : null, // Apply white background to the selected item
                                          borderRadius: isSelected ? BorderRadius.circular(AppSize.r6) : null,
                                        ),
                                        child: Center(
                                          child: Text(
                                            scheduleList[index],
                                            style: context.textTheme.bodyMedium?.copyWith(
                                              color: context.themeColors.textColor,
                                              fontSize: AppSize.sp14,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  SpaceV(AppSize.h24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      BlocBuilder<ScheduleCubit, ScheduleState>(
                        builder: (ctx, state) {
                          return ValueListenableBuilder(
                            valueListenable: selectValue,
                            builder: (context, value, child) {
                              return Container(
                                padding: EdgeInsets.symmetric(horizontal: AppSize.w10, vertical: AppSize.h5),
                                decoration: BoxDecoration(
                                    color: AppColors.primaryColor, borderRadius: BorderRadius.circular(AppSize.r4)),
                                child: InkWell(
                                  onTap: () async {
                                    scheduleDayBloc.setDate(selectedValue: DateTime.now());
                                    scheduleBloc.selectDate = DateTime.now();
                                    await scheduleBloc.apiCall(context, value);
                                  },
                                  child: Text(AppLocalizations.of(context)!.today.toUpperCase(),
                                      style: context.textTheme.bodyMedium?.copyWith(
                                          color: AppColors.white,
                                          fontSize: AppSize.sp12,
                                          letterSpacing: 1,
                                          fontWeight: FontWeight.w500)),
                                ),
                              );
                            },
                          );
                        },
                      ),
                      BlocBuilder<ScheduleCubit, ScheduleState>(
                        builder: (context, state) {
                          return (selectValue.value == 2)
                              ? GestureDetector(
                                  onTap: () async {
                                    await showDialog(
                                      context: context,
                                      barrierDismissible: true,
                                      builder: (dialogContext) {
                                        return applicationReferenceShowDialog(
                                          dialogTitle: AppLocalizations.of(context)!.department,
                                          context: context,
                                          list: scheduleBloc.applicationReferenceList1,
                                          onChanged: (val) {
                                            print("Selected items:");
                                            val.forEach((item) {
                                              print("------------>${item.title}: ${item.isSelected}");
                                            });
                                            scheduleBloc.applicationReferenceList1.value = val.toList();
                                          },
                                          onOKPressed: () {
                                            scheduleBloc.selectedTitles.value = scheduleBloc
                                                .applicationReferenceList1.value
                                                .where((item) => item.isSelected)
                                                .map((item) => item.title)
                                                .join(', ');
                                            print("Selected items:----- ${scheduleBloc.selectedTitles.value}");

                                            scheduleBloc.filterApplicationReferenceListForWeek();

                                            Navigator.pop(dialogContext);
                                          },
                                        );
                                      },
                                    );
                                  },
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          ValueListenableBuilder(
                                            valueListenable: scheduleBloc.selectedTitles,
                                            builder: (context, value, child) {
                                              // final scheduleBloc.selectedTitles =
                                              //     value.where((item) => item.isSelected).map((item) => item.title).join(', ');

                                              return Text(scheduleBloc.selectedTitles.value,
                                                  style: context.textTheme.bodyMedium?.copyWith(
                                                      color: context.themeColors.textColor,
                                                      fontSize: AppSize.sp15,
                                                      height: 1.3));
                                            },
                                          ),
                                          SpaceH(AppSize.w4),
                                          Icon(
                                            Ionicons.caret_down_outline,
                                            size: AppSize.sp12,
                                            color: AppColors.greyColor,
                                          )
                                        ],
                                      )
                                    ],
                                  ),
                                )
                              : (selectValue.value == 3)
                                  ? GestureDetector(
                                      onTap: () async {
                                        await showDialog(
                                          context: context,
                                          barrierDismissible: true,
                                          builder: (dialogContext) {
                                            return applicationReferenceShowDialog(
                                              dialogTitle: AppLocalizations.of(context)!.department,
                                              context: context,
                                              list: scheduleBloc.applicationReferenceList1,
                                              onChanged: (val) {
                                                print("Selected items:");
                                                val.forEach((item) {
                                                  print("------------>${item.title}: ${item.isSelected}");
                                                });
                                                scheduleBloc.applicationReferenceList1.value = val.toList();
                                              },
                                              onOKPressed: () {
                                                scheduleBloc.selectedTitles.value = scheduleBloc
                                                    .applicationReferenceList1.value
                                                    .where((item) => item.isSelected)
                                                    .map((item) => item.title)
                                                    .join(', ');
                                                print("Selected items:----- ${scheduleBloc.selectedTitles.value}");
                                                // if (scheduleBloc.selectedTitles.value == 'Rooster, Ruilen, Open diensten') {
                                                //   scheduleBloc.scheduleMonthList1 = scheduleBloc.scheduleMonthList;
                                                // } else if (scheduleBloc.selectedTitles.value.contains('Ruilen') &&
                                                //     scheduleBloc.selectedTitles.value.contains('Open diensten')) {
                                                //   scheduleBloc.scheduleMonthList = scheduleBloc.scheduleMonthList.where(
                                                //     (data) {
                                                //       print("State: ${data.swap?.state}");
                                                //
                                                //       return data.swap is SwapMonthModel &&
                                                //           data.openService is OpenServiceModel &&
                                                //           data.swap?.state == 'Geaccepteerd' &&
                                                //           data.swap?.state != null;
                                                //     },
                                                //   ).toList();
                                                // } else if (scheduleBloc.selectedTitles.value == 'Open diensten') {
                                                //   scheduleBloc.scheduleMonthList1 = scheduleBloc.scheduleMonthList
                                                //       .where((data) => data.openService is OpenServiceModel)
                                                //       .toList();
                                                // } else if (scheduleBloc.selectedTitles.value == 'Ruilen') {
                                                //   scheduleBloc.scheduleMonthList1 = scheduleBloc.scheduleMonthList
                                                //       .where(
                                                //           (data) => data.swap is SwapMonthModel && data.swap?.state == 'Geaccepteerd')
                                                //       .toList();
                                                // } else if (scheduleBloc.selectedTitles.value == 'Rooster') {
                                                //   scheduleBloc.scheduleMonthList1 = scheduleBloc.scheduleMonthList.where((data) {
                                                //     print("Item: $data");
                                                //     print("Swap: ${data.swap}");
                                                //     print("State: ${data.swap?.state}");
                                                //     return data.swap is SwapMonthModel &&
                                                //         data.swap?.state == 'Aangevraagd' &&
                                                //         data.swap?.state == null;
                                                //   }).toList();
                                                // } else {
                                                //   scheduleBloc.scheduleMonthList1 = scheduleBloc.scheduleMonthList;
                                                // }
                                                scheduleBloc.filterApplicationReferenceListForMonth();

                                                Navigator.pop(dialogContext);
                                              },
                                            );
                                          },
                                        );
                                      },
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              ValueListenableBuilder(
                                                valueListenable: scheduleBloc.selectedTitles,
                                                builder: (context, value, child) {
                                                  // final scheduleBloc.selectedTitles =
                                                  //     value.where((item) => item.isSelected).map((item) => item.title).join(', ');

                                                  return Text(scheduleBloc.selectedTitles.value,
                                                      style: context.textTheme.bodyMedium?.copyWith(
                                                          color: context.themeColors.textColor,
                                                          fontSize: AppSize.sp15,
                                                          height: 1.3));
                                                },
                                              ),
                                              SpaceH(AppSize.w4),
                                              Icon(
                                                Ionicons.caret_down_outline,
                                                size: AppSize.sp12,
                                                color: AppColors.greyColor,
                                              )
                                            ],
                                          )
                                        ],
                                      ),
                                    )
                                  : (selectValue.value == 4)
                                      ? GestureDetector(
                                          onTap: () async {
                                            await showDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              builder: (dialogContext) {
                                                return applicationReferenceShowDialog(
                                                  dialogTitle: AppLocalizations.of(context)!.department,
                                                  context: context,
                                                  list: scheduleBloc.applicationReferenceList1,
                                                  onChanged: (val) {
                                                    print("Selected items:");
                                                    val.forEach((item) {
                                                      print("------------>${item.title}: ${item.isSelected}");
                                                    });
                                                    scheduleBloc.applicationReferenceList1.value = val.toList();
                                                  },
                                                  onOKPressed: () {
                                                    scheduleBloc.selectedTitles.value = scheduleBloc
                                                        .applicationReferenceList1.value
                                                        .where((item) => item.isSelected)
                                                        .map((item) => item.title)
                                                        .join(', ');
                                                    print("Selected items:----- ${scheduleBloc.selectedTitles.value}");

                                                    scheduleBloc.filterApplicationReferenceListForCalendar();

                                                    Navigator.pop(dialogContext);
                                                  },
                                                );
                                              },
                                            );
                                          },
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  ValueListenableBuilder(
                                                    valueListenable: scheduleBloc.selectedTitles,
                                                    builder: (context, value, child) {
                                                      // final scheduleBloc.selectedTitles =
                                                      //     value.where((item) => item.isSelected).map((item) => item.title).join(', ');

                                                      return Text(scheduleBloc.selectedTitles.value,
                                                          style: context.textTheme.bodyMedium?.copyWith(
                                                              color: context.themeColors.textColor,
                                                              fontSize: AppSize.sp15,
                                                              height: 1.3));
                                                    },
                                                  ),
                                                  SpaceH(AppSize.w4),
                                                  Icon(
                                                    Ionicons.caret_down_outline,
                                                    size: AppSize.sp12,
                                                    color: AppColors.greyColor,
                                                  )
                                                ],
                                              )
                                            ],
                                          ),
                                        )
                                      : Container();
                        },
                      )
                    ],
                  ),
                ],
              ),
            ),
            selectValue.value == 4 ? SpaceV(0) : SpaceV(AppSize.h10),
            Expanded(
              child: ValueListenableBuilder(
                builder: (context, value, child) {
                  String? swipeDirection;

                  void _onHorizontalDragUpdate(DragUpdateDetails details) {
                    if (details.primaryDelta! > 0) {
                      // Swiping right
                      swipeDirection = 'right';
                    } else if (details.primaryDelta! < 0) {
                      // Swiping left
                      swipeDirection = 'left';
                    }
                    // Update UI or perform other actions based on swipe direction
                    // setState(() {
                    //   // Example: Change background color based on swipe direction
                    //   // _backgroundColor = swipeDirection == 'left' ? Colors.transparent : Colors.transparent;
                    // });
                  }

                  void _onHorizontalDragEnd(DragEndDetails details) {
                    if (swipeDirection == 'left') {
                      // Handle swipe left event
                      scheduleBloc.updateDate(selectValue.value, 'right');
                      print("handle swipe left event");
                    } else if (swipeDirection == 'right') {
                      // Handle swipe right event
                      scheduleBloc.updateDate(selectValue.value, 'left');
                      print("handle swipe right event");
                    }
                  }

                  return GestureDetector(
                    onHorizontalDragUpdate: _onHorizontalDragUpdate,
                    onHorizontalDragEnd: _onHorizontalDragEnd,
                    child: Container(
                      color: _backgroundColor,
                      child: selectScreen(selectValue.value, context, scheduleBloc.selectDate),
                    ),
                  );
                },
                valueListenable: selectValue,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _backgroundColor = Colors.transparent; // Initial background color
}

selectScreen(int screenIndex, BuildContext context, DateTime selectedValue) {
  switch (screenIndex) {
    case 1:
      return ScheduleDayScreen();
    case 2:
      return ScheduleWeekScreen();
    case 3:
      return ScheduleMonthScreen();
    case 4:
      return ScheduleCalenderScreen();
    default:
      return ScheduleDayScreen();
  }
}
