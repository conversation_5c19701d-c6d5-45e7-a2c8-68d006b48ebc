import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:ionicons/ionicons.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/common/custom_widgets/spacebox.dart';
import 'package:staff_medewerker/screens/pin_module/bloc/pin_screen_cubit.dart';
import 'package:staff_medewerker/screens/pin_module/circle_indicator.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/appsize.dart';
import 'package:staff_medewerker/utils/asset_path/assets_path.dart';
import 'package:staff_medewerker/utils/colors/app_colors.dart';

import '../authentication_module/login_module/ui/login_screen.dart';

class EnterPinPage extends StatelessWidget {
  final bool isRemovePinScreen;
  final bool isNavigateFromHomeScreen;
  final bool isFromHomeScreen;

  const EnterPinPage(
      {super.key,
      required this.isRemovePinScreen,
      required this.isNavigateFromHomeScreen,
      this.isFromHomeScreen = false});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.themeColors.primaryColor,
      appBar: isRemovePinScreen
          ? AppBar(
              automaticallyImplyLeading: false,
              elevation: 0,
              backgroundColor: Colors.transparent,
              actions: [
                IconButton(
                    onPressed: () {
                      AppNavigation.previousScreen(context);
                    },
                    icon: Icon(Ionicons.close))
              ],
            )
          : null,
      body: BlocBuilder<PinSetCubit, PinScreenMode>(
        builder: (ctx, state) {
          final pinBloc = BlocProvider.of<PinSetCubit>(context, listen: true);
          // final isPinSet = pinBloc.isPinSet.value;

          // // Determine the initial screen based on whether the PIN is set or not
          // PinScreenMode initialScreen = isPinSet ? PinScreenMode.EnterOldPin : PinScreenMode.SetFirstTimePin;
          // String pinScreenTitle = "";
          // if(initialScreen == PinScreenMode.EnterOldPin){
          //   pinScreenTitle = AppLocalizations.of(context)!.enterYourPinText;
          // }else if(initialScreen == PinScreenMode.RepeatSecondTimePin){
          //   pinScreenTitle = AppLocalizations.of(context)!.enterPinSecondTimeText;
          // }else if(initialScreen == PinScreenMode.SetFirstTimePin){
          //   pinScreenTitle = AppLocalizations.of(context)!.enterPinFirstTimeText;
          // }
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SpaceV(AppSize.h16),
                !isNavigateFromHomeScreen
                    ? ValueListenableBuilder(
                        valueListenable: pinBloc.pinScreenTitle,
                        builder: (BuildContext context, String pinScreenTitle, Widget? child) {
                          return Text(
                              isRemovePinScreen ? AppLocalizations.of(context)!.enterYourPinText : pinScreenTitle,
                              style: context.textTheme.bodyLarge
                                  ?.copyWith(color: AppColors.white, letterSpacing: 1, fontSize: AppSize.sp16));
                        },
                      )
                    : Image.asset(AssetsPath.mainWhiteLogo, height: AppSize.h60),
                SpaceV(AppSize.h70),
                ValueListenableBuilder(
                  valueListenable: pinBloc.enteredPinNumber,
                  builder: (context, pinNumber, child) {
                    return CircleIndicator(
                      pin: pinNumber,
                    );
                  },
                ),
                SpaceV(AppSize.h70),
                GridView.builder(
                  padding: EdgeInsets.symmetric(horizontal: AppSize.sp36),
                  shrinkWrap: true,
                  itemCount: 12,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: AppSize.sp15,
                    childAspectRatio: 2.8,
                    mainAxisSpacing: AppSize.sp15,
                  ),
                  itemBuilder: (BuildContext context, index) {
                    if (index == 9 || index == 11) {
                      return index == 9 ? Container() : EraseButton();
                    } else if (index == 10) {
                      return NumericButton(
                        number: "0",
                        isNavigateFromHomeScreen: isNavigateFromHomeScreen,
                        isFromHomeScreen: isFromHomeScreen,
                      );
                    } else {
                      return NumericButton(
                        number: "${(index + 1).toString()}",
                        isNavigateFromHomeScreen: isNavigateFromHomeScreen,
                        isFromHomeScreen: isFromHomeScreen,
                      );
                    }
                  },
                ),
                isNavigateFromHomeScreen
                    ? Padding(
                        padding: EdgeInsets.only(top: AppSize.sp30),
                        child: GestureDetector(
                          onTap: () {
                            AppNavigation.nextScreen(context, LoginScreen(isFromPinScreen: true));
                          },
                          child: Text(
                            AppLocalizations.of(context)!.forgotPinText,
                            style:
                                context.textTheme.bodyLarge?.copyWith(fontSize: AppSize.sp14, color: AppColors.white),
                          ),
                        ),
                      )
                    : Container()
              ],
            ),
          );
        },
      ),
    );
  }
}

class NumericButton extends StatelessWidget {
  final String number;
  final bool isNavigateFromHomeScreen;
  final bool isFromHomeScreen;
  NumericButton({required this.number, required this.isNavigateFromHomeScreen, this.isFromHomeScreen = false});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PinSetCubit, PinScreenMode>(
      builder: (ctx, state) {
        return OutlinedButton(
          onPressed: () {
            ctx.read<PinSetCubit>().addPinNumber(
                context: context,
                numberPressed: number,
                isNavigateFromHomeScreen: isNavigateFromHomeScreen,
                isFromHomeScreen: isFromHomeScreen);
          },
          style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppColors.white, width: 2), foregroundColor: AppColors.white),
          child: Text(
            number,
            style: context.textTheme.bodyLarge?.copyWith(fontSize: AppSize.sp14, color: AppColors.white),
          ),
        );
      },
    );
  }
}

class EraseButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PinSetCubit, PinScreenMode>(
      builder: (ctx, state) {
        return Container(
          child: OutlinedButton(
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: Colors.transparent,
              ),
            ),
            onPressed: () {
              ctx.read<PinSetCubit>().removePinNumber(
                    context: context,
                  );
            },
            child: Icon(Icons.backspace, size: AppSize.sp28, color: AppColors.white),
          ),
        );
      },
    );
  }
}
