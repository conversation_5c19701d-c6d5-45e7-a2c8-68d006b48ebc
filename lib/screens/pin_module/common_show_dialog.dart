import 'package:flutter/material.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/utils/appsize.dart';

class CustomAlertDialog extends StatelessWidget {
  final BuildContext context;
  final String title;
  final String message;
  final String? cancelText;
  final VoidCallback onOkPressed;
  final VoidCallback? cancelPressed;
  final bool? isCancelButton;
  String? okButtonText;

  CustomAlertDialog({
    required this.context,
    required this.title,
    required this.message,
    required this.onOkPressed,
    this.isCancelButton,
    this.okButtonText,
    this.cancelText,
    this.cancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: context.themeColors.cardColor,
      title: Text(
        title,
        style: context.textTheme.headlineLarge?.copyWith(
          fontSize: AppSize.sp18,
          fontWeight: FontWeight.w500,
          color: context.themeColors.textColor,
        ),
      ),
      contentPadding: EdgeInsets.only(top: AppSize.h12, bottom: AppSize.h10, left: AppSize.w20, right: AppSize.w20),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Text(
          message,
          style: context.textTheme.bodyMedium?.copyWith(
            fontSize: AppSize.sp15,
            fontWeight: FontWeight.normal,
            color: context.themeColors.darkGreyColor,
          ),
        ),
      ),
      actions: [
        isCancelButton == null
            ? Container()
            : TextButton(
                onPressed: cancelPressed ??
                    () {
                      Navigator.pop(context);
                    },
                child: Text(
                  cancelText ?? AppLocalizations.of(context)!.cancelText.toUpperCase(),
                  style: context.textTheme.bodyLarge?.copyWith(
                    fontSize: AppSize.sp12,
                    color: context.themeColors.primaryColor,
                  ),
                ),
              ),
        TextButton(
          onPressed: () {
            onOkPressed();
          },
          child: Text(
            okButtonText?.toUpperCase() ?? "OK".toUpperCase(),
            style: context.textTheme.bodyLarge?.copyWith(
              fontSize: AppSize.sp12,
              color: context.themeColors.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}

// CustomAlertDialog(context: context, title: AppLocalizations.of(context)!.pinSetText, message: AppLocalizations.of(context)!.pinSetMessageText, onOkPressed: () {
// AppNavigation.previousScreen(context);
// },);
// Prefs().setString.(AppConstants.savedPinNumber, repeatedPin);
