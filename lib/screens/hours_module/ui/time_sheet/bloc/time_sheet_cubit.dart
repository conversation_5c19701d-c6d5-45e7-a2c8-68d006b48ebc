import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/common_snackbar.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/bloc/hours_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_main_screen.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_week/bloc/hours_week_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/cost_centers_non_time_bound_person.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/get_department_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/my_timesheet_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/models/task_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/cost_centers_non_time_bound_person.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/get_department_setting.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/my_timesheet_data_save.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/my_timesheet_repository.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/repository/timesheet_empty_row.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/ui/activity_screen/bloc/activity_cubit.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';
import 'package:staff_medewerker/utils/date_language_convter.dart';

part 'time_sheet_state.dart';

class TimeSheetCubit extends Cubit<TimeSheetState> {
  TimeSheetCubit() : super(TimeSheetInitial());

  ValueNotifier<String> selectedDateString = ValueNotifier('');
  ValueNotifier<bool> isLoading = ValueNotifier(false);
  ValueNotifier<List<MyTimeSheetResponseModel>> myTimeSheetList = ValueNotifier([]);
  List<MyTimeSheetResponseModel> timeSheetEmptyRowList = [];
  List<CostCentersNonTimeBoundPersonModel> costCentersNonTimeBoundPersonList = [];
  List<String> newItemStartList = [];
  List<GetDepartmentSettingModel> getDepartmentSettingList = [];
  List<TaskResponseModel> taskList = [];
  int index = 0;
  String? selectActivity = '';
  // bool isRemark1Required = false;
  // bool isRemark2Required = false;
  // bool isRemark3Required = false;
  bool remarkValidate1 = false;
  bool remarkValidate2 = false;
  bool remarkValidate3 = false;
  bool isRemark = false;

  ValueNotifier<bool> isBreakSelectable = ValueNotifier(false);
  ValueNotifier<String> totalSelectedTime = ValueNotifier('00:00');
  TextEditingController remarkTextController1 = TextEditingController();
  TextEditingController remarkTextController2 = TextEditingController();
  TextEditingController remarkTextController3 = TextEditingController();
  TextEditingController remarkTextController = TextEditingController();

  String? selectedDepartmentName;
  String? selectedDepartmentId;
  String? selectedTaskName;
  String? selectedTaskId;
  String selectedActivityName1 = "Activity";
  String? selectedActivityId1;
  String selectedActivityName2 = "Activity";
  String? selectedActivityId2;
  String selectedActivityName3 = "Activity";
  String? selectedActivityId3;

  //clear all time data
  clearAllData(bool isFromEdit) {
    log(' isFromEdit isFromEdit $isFromEdit');
    selectedDepartmentName = "";
    selectedDepartmentId = "";
    selectedActivityName1 = "Activity";
    selectedActivityId1 = "";
    selectedActivityName2 = "Activity";
    selectedActivityId2 = "";
    selectedActivityName3 = "Activity";
    selectedActivityId3 = "";
    selectedTaskName = AppLocalizations.of(navigatorKey.currentContext!)!.selectedTask;
    selectedTaskId = "";
    // isFromEdit == false
    //     ? myTimeSheetList.value[index].breakTime = "00:00"
    //     : myTimeSheetList.value[index].breakTime = myTimeSheetList.value[index].breakTime;
    // isFromEdit == false
    //     ? myTimeSheetList.value[index].totalHours = "00:00"
    //     : myTimeSheetList.value[index].totalHours = myTimeSheetList.value[index].totalHours;
  }

  void updateBreakSelectable(bool value, int index) {
    isBreakSelectable.value = value;
    emit(TimeSheetInitial());
  }

  void deleteData(TimeSheetCubit timeBloc) {
    timeBloc.myTimeSheetList.value.removeAt(index);
    timeBloc.newItemStartList.removeAt(index);
  }

  String extractDayFromTitle(String dateString) {
    try {
      final inputFormat = DateFormat("EEEE dd MMMM", appDB.language);

      // Parse the date string into a DateTime object
      DateTime dateTime = inputFormat.parse(dateString);

      // Define the output format to extract the day (dd)
      final outputFormat = DateFormat("dd");

      // Format the DateTime object to get the day
      final formattedDay = outputFormat.format(dateTime);

      // Return the formatted day
      return formattedDay;
    } catch (e) {
      // Log the error for debugging

      return "Invalid Date";
    }
  }

  static formatDate(DateTime date) {
    final formatter = DateFormat('EEEE dd MMMM yyyy');
    return formatter.format(date);
  }

  Future<void> timeSheetApiCall({required BuildContext context, required String isoStartDate}) async {
    isLoading.value = true;
    print("api started =====>");
    final MyTimeSheetApiRepository myTimeSheetApiRepository = MyTimeSheetApiRepository();
    final response = await myTimeSheetApiRepository.myTimeSheetApi(context: context, isoStartDate: isoStartDate);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      myTimeSheetList.value.clear();
      newItemStartList.clear();
      myTimeSheetList.value.addAll(response);
      print("myTimeSheetList.value.length - 1${response}");
      newItemStartList = List.generate(myTimeSheetList.value.length, (index) => '');
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    isLoading.value = false;
    emit(TimeSheetInitial());
  }

  Future<void> costCentersNonTimeBoundPersonApi(
      {required BuildContext context, required String startIsoDate, required String endIsoDate}) async {
    print("api started =====>");
    final CostCentersNonTimeBoundPersonApiRepository costCentersNonTimeBoundPersonApiRepository =
        CostCentersNonTimeBoundPersonApiRepository();
    final response = await costCentersNonTimeBoundPersonApiRepository.costCentersNonTimeBoundPersonApi(
        context: context, startIsoDate: startIsoDate, endIsoDate: endIsoDate);
    print("api done =====>${response}");

    if (response!.isNotEmpty) {
      costCentersNonTimeBoundPersonList.clear();
      costCentersNonTimeBoundPersonList.addAll(response);
    } else {
      // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
    }
    emit(TimeSheetInitial());
  }

  String extractDateFromTitle(String dayTitle) {
    dayTitle = dayTitle.trim();
    String formattedDate = formatDateString(dayTitle);
    print(formattedDate); // Output: 0921
    return formattedDate;
  }

  String formatDateString(String inputDate) {
    // Get the current locale from the context
    // Locale currentLocale = Localizations.localeOf(context);

    // Define the input format based on the current locale
    final inputFormat = DateFormat("EEEE dd MMMM", appDB.language);

    // Parse the input string into a DateTime object
    inputDate = inputDate.replaceAll(RegExp(r'at\s+\d+.*'), '').trim();

    // Try to parse the input string
    try {
      DateTime dateTime = inputFormat.parse(inputDate);

      // Define the output format (e.g., MMdd)
      final outputFormat = DateFormat("MMdd");

      // Format the DateTime object to the desired format
      final formattedDate = outputFormat.format(dateTime);

      return formattedDate;
    } catch (e) {
      // Handle parsing errors
      print("Error parsing date: $e");
      return ''; // Return an empty string or a default value
    }
  }

  void setDate({required DateTime selectedValue}) {
    selectedDateString.value = formatDate(selectedValue);
  }

  //time function
  Future<void> selectFromTime(BuildContext context, int index, {required String fromTime}) async {
    // Future<void> selectFromTime(BuildContext context, int index) async {
    print(myTimeSheetList.value[index].timeFrom);
    // final TimeOfDay initialTime = myTimeSheetList.value[index].timeFrom?.isNotEmpty ?? false
    //     ? TimeOfDay(
    //         hour: int.parse((myTimeSheetList.value[index].timeFrom ?? "00:00").split(':')[0]),
    //         minute: int.parse((myTimeSheetList.value[index].timeFrom ?? "00:00").split(':')[1]),
    //       )
    //     : TimeOfDay(
    //         hour: 12,
    //         minute: 00,
    //       );
    //
    // final TimeOfDay? newTime = await showTimePicker(
    //   context: context,
    //   initialTime: initialTime,
    //   cancelText: AppLocalizations.of(context)!.closeText.toUpperCase(),
    //   confirmText: AppLocalizations.of(context)!.oK.toUpperCase(),
    // );
    //
    // if (newTime != null) {
    //   myTimeSheetList.value[index].timeFrom =
    //       "${newTime.hour.toString().padLeft(2, '0')}:${newTime.minute.toString().padLeft(2, '0')}";
    // }

    myTimeSheetList.value[index].timeFrom = fromTime;
    if (myTimeSheetList.value[index].timeUntil != "00:00") {
      fetchBreakTime(context);
      calculateTotalOfFromUntilBreakTime(context);
    }

    // myTimeSheetList.value[index].timeFrom = myTimeSheetList.value[index].timeFrom;
    myTimeSheetList.notifyListeners();

    myTimeSheetList.value[index].breakTime = "00:00";
    fetchBreakTime(context);
    calculateTotalOfFromUntilBreakTime(context);
    myTimeSheetList.value[index].tb1Hours = totalSelectedTime.value;

    emit(TimeSheetInitial());
  }

  Future<void> selectUntilTime(BuildContext context, int index, {required String untilTime}) async {
    // final TimeOfDay initialTime = myTimeSheetList.value[index].timeUntil?.isNotEmpty ?? false
    //     ? TimeOfDay(
    //         hour: int.parse((myTimeSheetList.value[index].timeUntil ?? "00:00").split(':')[0]),
    //         minute: int.parse((myTimeSheetList.value[index].timeUntil ?? "00:00").split(':')[1]),
    //       )
    //     : TimeOfDay(
    //         hour: 00,
    //         minute: 00,
    //       );
    //
    // final TimeOfDay? newTime = await showTimePicker(
    //   context: context,
    //   initialTime: initialTime,
    //   cancelText: AppLocalizations.of(context)!.closeText.toUpperCase(),
    //   confirmText: AppLocalizations.of(context)!.oK.toUpperCase(),
    // );
    //
    // if (newTime != null) {
    //   myTimeSheetList.value[index].timeUntil =
    //       "${newTime.hour.toString().padLeft(2, '0')}:${newTime.minute.toString().padLeft(2, '0')}";
    // }

    myTimeSheetList.value[index].timeUntil = untilTime;

    myTimeSheetList.value[index].breakTime = "00:00";
    fetchBreakTime(context);
    calculateTotalOfFromUntilBreakTime(context);

    myTimeSheetList.value[index].tb1Hours = totalSelectedTime.value;
    emit(TimeSheetInitial());
  }

  void calculateTotalOfFromUntilBreakTime(BuildContext context) {
    final hourBloc = context.read<HoursCubit>();

    totalSelectedTime.value = hourBloc.calculateTotalHours(
      date: DateTime.now(),
      timeFrom: myTimeSheetList.value[index].timeFrom ?? "00:00",
      timeUntil: myTimeSheetList.value[index].timeUntil ?? "00:00",
      breakTime: myTimeSheetList.value[index].breakTime ?? "00:00",
    );
  }

  Future<void> selectBreakTime(BuildContext context, {required String breakTime}) async {
    // final TimeOfDay initialTime = myTimeSheetList.value[index].breakTime?.isNotEmpty ?? false
    //     ? TimeOfDay(
    //         hour: int.parse((myTimeSheetList.value[index].breakTime ?? "00:00").split(':')[0]),
    //         minute: int.parse((myTimeSheetList.value[index].breakTime ?? "00:00").split(':')[1]),
    //       )
    //     : TimeOfDay.now();
    //
    // final TimeOfDay? newTime = await showTimePicker(
    //   context: context,
    //   initialTime: initialTime,
    //   cancelText: AppLocalizations.of(context)!.closeText.toUpperCase(),
    //   confirmText: AppLocalizations.of(context)!.oK.toUpperCase(),
    // );
    //
    // if (newTime != null) {
    //   myTimeSheetList.value[index].breakTime =
    //       "${newTime.hour.toString().padLeft(2, '0')}:${newTime.minute.toString().padLeft(2, '0')}";
    // }

    myTimeSheetList.value[index].breakTime = breakTime;
    fetchBreakTime(context);
    calculateTotalOfFromUntilBreakTime(context);

    emit(TimeSheetInitial());
  }

  //tb1Hours = "01:26"
  //tb2Hours = "06:00"
  //tb3Hours = "00:00"
  //totalSelectedTime.value = "07:26"

  // if tb3Hours i make "01:00" then automatic become tb1Hours = "00:26"

  // void checkTotalAndActivityTime() {
  //   final List<String> time1Parts = (myTimeSheetList.value[index].tb1Hours ?? "00:00").split(":");
  //   final List<String> time2Parts = (myTimeSheetList.value[index].tb2Hours ?? "00:00").split(":");
  //   final List<String> time3Parts = (myTimeSheetList.value[index].tb3Hours ?? "00:00").split(":");
  //
  //   int totalActivityHours = int.parse(time1Parts[0]) + int.parse(time2Parts[0]) + int.parse(time3Parts[0]);
  //   int totalActivityMinutes = int.parse(time1Parts[1]) + int.parse(time2Parts[1]) + int.parse(time3Parts[1]);
  //
  //   int minutesCarry = (totalActivityMinutes ~/ 60); // Calculate the carry-over minutes
  //
  //   if (minutesCarry > 0) {
  //     totalActivityHours += minutesCarry;
  //     totalActivityMinutes %= 60;
  //   }
  //
  //   print("Total hours: $totalActivityHours");
  //   print("Total minutes: $totalActivityMinutes");
  //
  //   final List<String> totalTimeParts = totalSelectedTime.value.split(":");
  //
  //   int totalSelectedHour = int.parse(totalTimeParts[0]);
  //   int totalSelectedMinutes = int.parse(totalTimeParts[1]);
  //
  //   if (totalSelectedHour == totalActivityHours && totalSelectedMinutes == totalActivityMinutes) {
  //     print("Matched");
  //     isTotalMatched.value = true;
  //   } else if (totalSelectedHour > totalActivityHours) {
  //     int totalHoursDiff = totalSelectedHour - totalActivityHours;
  //
  //     // Check if time1Parts[0] is not zero, and distribute the difference.
  //     if (int.parse(time1Parts[0]) != 0) {
  //       myTimeSheetList.value[index].tb1Hours?.split(':')[0] =
  //           (int.parse(time1Parts[0]) + totalHoursDiff).toString().padLeft(2, '0');
  //
  //       print("Not Matched");
  //     }
  //   }
  //   emit(TimeSheetInitial());
  // }

  void checkTotalAndActivityTime() {
    final List<String> time1Parts = (myTimeSheetList.value[index].tb1Hours ?? "00:00").split(":");
    final List<String> time2Parts = (myTimeSheetList.value[index].tb2Hours ?? "00:00").split(":");
    final List<String> time3Parts = (myTimeSheetList.value[index].tb3Hours ?? "00:00").split(":");
    print("Total hours: ${time1Parts}");
    print("Total hours: ${time2Parts}");
    print("Total hours: ${time3Parts}");
    int totalActivityHours = int.parse(time1Parts[0]) + int.parse(time2Parts[0]) + int.parse(time3Parts[0]);
    int totalActivityMinutes = int.parse(time1Parts[1]) + int.parse(time2Parts[1]) + int.parse(time3Parts[1]);

    int minutesCarry = (totalActivityMinutes ~/ 60); // Calculate the carry-over minutes

    if (minutesCarry > 0) {
      totalActivityHours += minutesCarry;
      totalActivityMinutes %= 60;
    }
    String formattedMinutes = totalActivityMinutes.toString().padLeft(2, '0');
    String formattedHours = totalActivityHours.toString().padLeft(2, '0');

    print("Total hours: ${totalActivityHours}");
    print("Total minutes: $formattedMinutes");
    print("Total minutes: ${totalSelectedTime.value.split(":")}");

    final List<String> totalTimeParts = totalSelectedTime.value.split(":");

    int totalSelectedHour = int.parse(totalTimeParts[0]);
    int totalSelectedMinutes = int.parse(totalTimeParts[1]);

    if (totalSelectedHour != totalActivityHours || totalSelectedMinutes != totalActivityMinutes) {
      // Calculate the difference between totalSelectedTime and totalActivityTime
      int diffHours = totalActivityHours - totalSelectedHour;
      int diffMinutes = totalActivityMinutes - totalSelectedMinutes;

      // Update time1Parts, time2Parts, and time3Parts accordingly
      if (diffHours > 0) {
        int remainingHours = diffHours;

        if (int.parse(time1Parts[0]) > 0) {
          int deductedHours = int.parse(time1Parts[0]);
          if (deductedHours >= remainingHours) {
            time1Parts[0] = (deductedHours - remainingHours).toString().padLeft(2, '0');
            remainingHours = 0;
          } else {
            time1Parts[0] = '00';
            remainingHours -= deductedHours;
          }
        }

        if (remainingHours > 0 && int.parse(time2Parts[0]) > 0) {
          int deductedHours = int.parse(time2Parts[0]);
          if (deductedHours >= remainingHours) {
            time2Parts[0] = (deductedHours - remainingHours).toString().padLeft(2, '0');
            remainingHours = 0;
          } else {
            time2Parts[0] = '00';
            remainingHours -= deductedHours;
          }
        }

        if (remainingHours > 0 && int.parse(time3Parts[0]) > 0) {
          time3Parts[0] = (int.parse(time3Parts[0]) - remainingHours).toString().padLeft(2, '0');
        }
      }

      if (diffMinutes > 0) {
        int remainingMinutes = diffMinutes;

        if (int.parse(time1Parts[1]) > 0) {
          int deductedMinutes = int.parse(time1Parts[1]);
          if (deductedMinutes >= remainingMinutes) {
            time1Parts[1] = (deductedMinutes - remainingMinutes).toString().padLeft(2, '0');
            remainingMinutes = 0;
          } else {
            time1Parts[1] = '00';
            remainingMinutes -= deductedMinutes;
          }
        }

        if (remainingMinutes > 0 && int.parse(time2Parts[1]) > 0) {
          int deductedMinutes = int.parse(time2Parts[1]);
          if (deductedMinutes >= remainingMinutes) {
            time2Parts[1] = (deductedMinutes - remainingMinutes).toString().padLeft(2, '0');
            remainingMinutes = 0;
          } else {
            time2Parts[1] = '00';
            remainingMinutes -= deductedMinutes;
          }
        }

        if (remainingMinutes > 0 && int.parse(time3Parts[1]) > 0) {
          time3Parts[1] = (int.parse(time3Parts[1]) - remainingMinutes).toString().padLeft(2, '0');
        }
      }
    }

    // Update the time pickers with the adjusted values
    myTimeSheetList.value[index].tb1Hours = "${time1Parts[0]}:${time1Parts[1]}";
    myTimeSheetList.value[index].tb2Hours = "${time2Parts[0]}:${time2Parts[1]}";
    myTimeSheetList.value[index].tb3Hours = "${time3Parts[0]}:${time3Parts[1]}";

    // Recalculate totalActivityHours and totalActivityMinutes
    totalActivityHours = int.parse(time1Parts[0]) + int.parse(time2Parts[0]) + int.parse(time3Parts[0]);
    totalActivityMinutes = int.parse(time1Parts[1]) + int.parse(time2Parts[1]) + int.parse(time3Parts[1]);
    formattedMinutes = totalActivityMinutes.toString().padLeft(2, '0');
    formattedHours = totalActivityHours.toString().padLeft(2, '0');
    // Update isError based on the new calculation
    if (totalSelectedHour == totalActivityHours && totalSelectedMinutes == totalActivityMinutes) {
      myTimeSheetList.value[index].isError = false;
    } else {
      myTimeSheetList.value[index].isError = true;
    }

    myTimeSheetList.value[index].totalActivityHours = '${formattedHours}:${formattedMinutes}';
    myTimeSheetList.value[index].totalHours = totalSelectedTime.value;

    print("myTimeSheetList.value[index].totalActivityHours ${myTimeSheetList.value[index].totalActivityHours}");
    print("myTimeSheetList.value[index].totalActivityHours ${totalActivityHours}");
    print("myTimeSheetList.value[index].totalActivityHours ${totalActivityMinutes}");
    print("myTimeSheetList.value[index].totalActivityHours ${myTimeSheetList.value[index].totalHours}");

    emit(TimeSheetInitial());
  }

  // void checkTotalAndActivityTime() {
  //   final List<String> time1Parts = (myTimeSheetList.value[index].tb1Hours ?? "00:00").split(":");
  //   final List<String> time2Parts = (myTimeSheetList.value[index].tb2Hours ?? "00:00").split(":");
  //   final List<String> time3Parts = (myTimeSheetList.value[index].tb3Hours ?? "00:00").split(":");
  //
  //   int totalActivityHours = int.parse(time1Parts[0]) + int.parse(time2Parts[0]) + int.parse(time3Parts[0]);
  //   int totalActivityMinutes = int.parse(time1Parts[1]) + int.parse(time2Parts[1]) + int.parse(time3Parts[1]);
  //
  //   int minutesCarry = (totalActivityMinutes ~/ 60); // Calculate the carry-over minutes
  //
  //   if (minutesCarry > 0) {
  //     totalActivityHours += minutesCarry;
  //     totalActivityMinutes %= 60;
  //   }
  //
  //   print("Total hours: $totalActivityHours");
  //   print("Total minutes: $totalActivityMinutes");
  //
  //   final List<String> totalTimeParts = totalSelectedTime.value.split(":");
  //
  //   int totalSelectedHour = int.parse(totalTimeParts[0]);
  //   int totalSelectedMinutes = int.parse(totalTimeParts[1]);
  //
  //   if (totalSelectedHour == totalActivityHours && totalSelectedMinutes == totalActivityMinutes) {
  //     myTimeSheetList.value[index].isError = false;
  //   } else if (totalSelectedHour < totalActivityHours || totalSelectedMinutes < totalActivityMinutes) {
  //     // Calculate the remaining hours and minutes needed
  //
  //     int diffHours = totalActivityHours - totalSelectedHour;
  //     int diffMinutes = totalActivityMinutes - totalSelectedMinutes;
  //
  //     int tb1Hours = int.parse(time1Parts[0]);
  //     int tb1Minutes = int.parse(time1Parts[1]);
  //     // int tb2Hours = int.parse(time2Parts[0]);
  //     // int tb2Minutes = int.parse(time2Parts[1]);
  //     // int tb3Hours = int.parse(time3Parts[0]);
  //     // int tb3Minutes = int.parse(time3Parts[1]);
  //     if (diffHours >= int.parse(time1Parts[0])) {
  //       myTimeSheetList.value[index].tb1Hours =
  //           "${(int.parse(time1Parts[0]) - diffHours).toString().padLeft(2, '0')}:${(int.parse(time1Parts[1])).toString().padLeft(2, '0')}";
  //
  //       print("myTimeSheetList.value[index].tb1Hours ${myTimeSheetList.value[index].tb1Hours}");
  //     }
  //     if (diffMinutes >= int.parse(time1Parts[1])) {
  //       myTimeSheetList.value[index].tb1Hours =
  //           "${tb1Hours.toString().padLeft(2, '0')}:${(tb1Minutes - diffMinutes).toString().padLeft(2, '0')}";
  //
  //       print("myTimeSheetList.value[index].tb1Minutes ${myTimeSheetList.value[index].tb1Hours}");
  //     }
  //   }
  //   if (totalSelectedHour != totalActivityHours || totalSelectedMinutes != totalActivityMinutes) {
  //     myTimeSheetList.value[index].isError = true;
  //   }
  //
  //   if (totalSelectedHour == totalActivityHours && totalSelectedMinutes == totalActivityMinutes) {
  //     myTimeSheetList.value[index].isError = false;
  //   }
  //   myTimeSheetList.value[index].totalActivityHours = '${totalActivityHours}:${totalActivityMinutes}';
  //   myTimeSheetList.value[index].totalHours = totalSelectedTime.value;
  //
  //   print("myTimeSheetList.value[index].totalActivityHours${myTimeSheetList.value[index].totalActivityHours}");
  //   print("myTimeSheetList.value[index].totalActivityHours${myTimeSheetList.value[index].totalHours}");
  //   emit(TimeSheetInitial());
  // }

  void updateDepartmentValue(value, id) {
    selectedDepartmentName = value;
    selectedDepartmentId = id;
    emit(TimeSheetInitial());
  }

  void updateTaskValue(value, id) {
    selectedTaskName = value;
    selectedTaskId = id;
    emit(TimeSheetInitial());
  }

  void updateActivityValue1(value, id) {
    selectedActivityName1 = value;
    selectedActivityId1 = id;
    emit(TimeSheetInitial());
  }

  void updateActivityValue2(value, id) {
    selectedActivityName2 = value;
    selectedActivityId2 = id;
    emit(TimeSheetInitial());
  }

  void updateActivityValue3(value, id) {
    selectedActivityName3 = value;
    selectedActivityId3 = id;
    emit(TimeSheetInitial());
  }

  void updateLunchValue() {
    myTimeSheetList.value[index].ntb1Checked = myTimeSheetList.value[index].ntb1Checked ?? false;
    if (myTimeSheetList.value[index].ntb1Checked == false) {
      myTimeSheetList.value[index].ntb1Checked = true;
    } else {
      myTimeSheetList.value[index].ntb1Checked = false;
    }
    emit(TimeSheetInitial());
  }

  void updateMealValue() {
    myTimeSheetList.value[index].ntb2Checked = myTimeSheetList.value[index].ntb2Checked ?? false;
    if (myTimeSheetList.value[index].ntb2Checked == false) {
      myTimeSheetList.value[index].ntb2Checked = true;
    } else {
      myTimeSheetList.value[index].ntb2Checked = false;
    }
    emit(TimeSheetInitial());
  }

  void updateChange() {
    myTimeSheetList.value[index].ntb3Checked = myTimeSheetList.value[index].ntb3Checked ?? false;
    if (myTimeSheetList.value[index].ntb3Checked == false) {
      myTimeSheetList.value[index].ntb3Checked = true;
    } else {
      myTimeSheetList.value[index].ntb3Checked = false;
    }
    emit(TimeSheetInitial());
  }

  void updateWorkHome() {
    myTimeSheetList.value[index].ntb4Checked = myTimeSheetList.value[index].ntb4Checked ?? false;
    if (myTimeSheetList.value[index].ntb4Checked == false) {
      myTimeSheetList.value[index].ntb4Checked = true;
    } else {
      myTimeSheetList.value[index].ntb4Checked = false;
    }
    emit(TimeSheetInitial());
  }

  Future<void> getTimeSheetEmptyRow({required BuildContext context, required String iSODate}) async {
    isLoading.value = true;
    print("api started =====>");
    final TimeSheetEmptyRowApiRepository timeSheetEmptyRowApiRepository = TimeSheetEmptyRowApiRepository();
    final response;
    try {
      response = await timeSheetEmptyRowApiRepository.timeSheetEmptyRowApi(context: context, iSODate: iSODate);
      if (response!.isNotEmpty) {
        timeSheetEmptyRowList.clear();
        timeSheetEmptyRowList.addAll(response);
        timeSheetEmptyRowList.forEach(
          (element) {
            if (element.timeFrom == null && element.timeUntil == null) {
              element.timeFrom = '00:00';
              element.timeUntil = '00:00';
              element.breakTime = '00:00';
            }
            print("elementbreakTime ${element.breakTime}");
            print("elementbreakTime ${element.timeFrom}");
            print("elementbreakTime ${element.timeUntil}");
          },
        );
        print("timeSheetEmptyRowListapi done =====>${timeSheetEmptyRowList}");
      } else {
        // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      }
    } finally {
      isLoading.value = false;
      emit(TimeSheetInitial());
    }
  }

  Future<void> getDepartmentSetting({required BuildContext context, required String guid}) async {
    isLoading.value = true;
    print("api started =====>");
    final GetDepartmentSettingApiRepository getDepartmentSettingApiRepository = GetDepartmentSettingApiRepository();
    final response;
    try {
      response = await getDepartmentSettingApiRepository.getDepartmentSettingApi(context: context, guid: guid);
      if (response!.isNotEmpty) {
        getDepartmentSettingList.clear();
        getDepartmentSettingList.addAll(response);
      } else {
        // customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText,actionButtonText: AppLocalizations.of(context)!.closeText.toUpperCase());
      }
    } finally {
      isLoading.value = false;
      emit(TimeSheetInitial());
    }
    print("api done =====>${response}");
  }

  Future<void> myTimesheetDataSaveApi({required BuildContext context, required String myTimeSheetListJson}) async {
    final MyTimeSheetDataSaveApiRepository myTimeSheetDataSaveApiRepository = MyTimeSheetDataSaveApiRepository();
    print("myTimesheetDataSaveApi done =====>${myTimesheetDataSaveApi}");

    try {
      final response = await myTimeSheetDataSaveApiRepository.timeSheetDataSaveApi(
          context: context, myTimeSheetListJson: myTimeSheetListJson);
      if (response?.done == true) {
        final date = DateTime.now();
        context.read<HoursWeekCubit>().allHoursWeekList = {};
        context.read<HoursCubit>().hoursMonthApiCall(
            context: context,
            iSOYearMonthStart: "${date.year - 100}${DateFormatFunctions.formatDay(date.month)}",
            iSOYearMonthEnd: "${date.year}${DateFormatFunctions.formatDay(date.month)}",
            isFirstTime: true);

        Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => HoursScreen(),
            ),
            (route) => false);

        customSnackBar(context: context, message: AppLocalizations.of(context)!.savedSuccessful);

        print("Done");
      } else {
        customSnackBar(context: context, message: AppLocalizations.of(context)!.errorText);
      }
    } finally {
      emit(TimeSheetInitial());
    }
  }

  void fetchBreakTime(BuildContext context) {
    // final timeBloc = BlocProvider.of(context);
    print("stringToDateTime ${getDepartmentSettingList[0].breakSettings}");
    if (getDepartmentSettingList[0].breakSettings?.length == 0) {
      updateBreakSelectable(true, index);
    } else {
      List<String> lastTime = ["0", "0"];
      getDepartmentSettingList[0].breakSettings?.forEach((element) {
        // DateTime startTime = stringToDateTime(element.startTime);
        //
        // print("Parsed DateTime: $startTime");
        //
        // DateTime totalSelectedTime2 = stringToDateTime(totalSelectedTime.value);
        //
        // if (totalSelectedTime2.isBefore(startTime)) {
        //   myTimeSheetList.value[index].breakTime = element.breakTime;
        //   print("added");
        // }
        // print("timeBloc.myTimeSheetList.value[widget.index].breakTime${myTimeSheetList.value[index].breakTime}");

        List<String> totalTime = totalSelectedTime.value.split(":");
        List<String> startTime = element.startTime?.split(":") ?? [];

        if (int.parse(totalTime[0]) > int.parse(startTime[0]) ||
            (int.parse(totalTime[0]) == int.parse(startTime[0]) && int.parse(totalTime[1]) > int.parse(startTime[1]))) {
          if (int.parse(lastTime[0]) < int.parse(startTime[0]) ||
              (int.parse(totalTime[0]) == int.parse(startTime[0]) &&
                  int.parse(totalTime[1]) > int.parse(startTime[1]))) {
            lastTime = startTime;
            myTimeSheetList.value[index].breakTime = element.breakTime;

            print("selected break time = ${element.breakTime}");
          }
        }
        updateBreakSelectable(false, index);
      });

      if (lastTime == ["0", "0"]) {
        myTimeSheetList.value[index].breakTime = "00:00";
      }
    }
    calculateTotalOfFromUntilBreakTime(context);
  }

  String dateToString(DateTime date) {
    final formatter = DateFormat('EEEE dd MMMM yyyy');
    return formatter.format(date);
  }

  DateTime stringToDate(String dateString) {
    final formatter = DateFormat('EEEE dd MMMM yyyy');
    return formatter.parse(DateLanguageConverter.normalizeDateToEnglish(dateString));
  }

  void rebuildScreen() {
    emit(TimeSheetInitial());
  }

  void checkRemarkRequiredOrNot({required BuildContext context}) {
    myTimeSheetList.value[index].tb1RemarkRequired = false;
    myTimeSheetList.value[index].tb2RemarkValidate = false;
    myTimeSheetList.value[index].tb3RemarkValidate = false;
    // isRemark1Required = false;
    // isRemark2Required = false;
    // isRemark3Required = false;

    final activityBloc = BlocProvider.of<ActivityCubit>(context);

    activityBloc.activityList.forEach((element) {
      if (element.costCenterId == selectedActivityId1) {
        element.nonStandard.forEach((element2) {
          if (element2.departmentId == selectedDepartmentId) {
            if (element2.remarkRequired) {
              print("first remark required");
              myTimeSheetList.value[index].tb1RemarkRequired = true;
            } else {
              print("first remark not required");
              myTimeSheetList.value[index].tb1RemarkRequired = false;
            }
          }
        });
      }

      if (element.costCenterId == selectedActivityId2) {
        element.nonStandard.forEach((element2) {
          if (element2.departmentId == selectedDepartmentId) {
            if (element2.remarkRequired) {
              print("second remark required");
              myTimeSheetList.value[index].tb2RemarkValidate = true;
            } else {
              print("second remark not required");
              myTimeSheetList.value[index].tb2RemarkValidate = false;
            }
          }
        });
      }

      if (element.costCenterId == selectedActivityId3) {
        element.nonStandard.forEach((element2) {
          if (element2.departmentId == selectedDepartmentId) {
            if (element2.remarkRequired) {
              print("third remark required");
              myTimeSheetList.value[index].tb3RemarkValidate = true;
            } else {
              print("third remark not required");
              myTimeSheetList.value[index].tb3RemarkValidate = false;
            }
          }
        });
      }
    });

    emit(TimeSheetInitial());
  }
}
