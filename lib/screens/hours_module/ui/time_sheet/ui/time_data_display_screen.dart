import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:staff_medewerker/l10n/app_localizations.dart';
import 'package:staff_medewerker/common/common_functions/common_dateformat_function.dart';
import 'package:staff_medewerker/common/custom_widgets/custom_theme/ext_build_context.dart';
import 'package:staff_medewerker/main.dart';
import 'package:staff_medewerker/screens/hours_module/models/hoursweek_response_model.dart';
import 'package:staff_medewerker/screens/hours_module/ui/hours_week/bloc/hours_week_cubit.dart';
import 'package:staff_medewerker/screens/hours_module/ui/time_sheet/bloc/time_sheet_cubit.dart';
import 'package:staff_medewerker/utils/app_navigation/appnavigation.dart';
import 'package:staff_medewerker/utils/constant/constant.dart';

import '../../../../../common/custom_widgets/appbar_custom.dart';
import '../../../../../common/custom_widgets/spacebox.dart';
import '../../../../../utils/appsize.dart';
import '../../../../../utils/asset_path/assets_path.dart';
import 'edit_time_sheet_screen.dart';

class TimeDataDisplayScreen extends StatelessWidget {
  final String dayNameTitle;
  List<HoursWeekResponseModel> monthlyData;
  final List<HoursWeekResponseModel> dayData1 = [];
  String selectedYear = "";

  TimeDataDisplayScreen({Key? key, required this.dayNameTitle, required this.monthlyData, required this.selectedYear})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final timeBloc = context.read<TimeSheetCubit>();

    dayData1.clear();
    monthlyData.forEach((element) {
      //element.date 2024-09-01 00:00:00.000
      print("element.date ${element.date.day}");
      print("dayNameTitledayNameTitle ${dayNameTitle}");

      final day = timeBloc.extractDayFromTitle(dayNameTitle);
      print("Day: $dayNameTitle");
      print("Day: ${DateFormatFunctions.formatDay(element.date.day)}");

      if (DateFormatFunctions.formatDay(element.date.day) == day) {
        log("innnn ${element.timeFrom}");
        dayData1.add(element);
        print("element.timeFrom ${element.timeFrom}");
      }
    });

    List<HoursWeekResponseModel> dayData = dayData1.reversed.toList();
    final hourBloc = context.read<HoursWeekCubit>();

    return Scaffold(
      backgroundColor: context.themeColors.homeContainerColor,
      appBar: CustomAppBar(
        actions: true,
        isLeading: true,
        centerTitle: true,
        title: dayNameTitle.toString(),
        actionList: [
          appDB.user?.AppFunctionRightsList?.contains(AppConstants.myHoursEditRights) ?? false
              ? Center(
                  child: GestureDetector(
                    onTap: () {
                      print("========>$dayNameTitle");
                      AppNavigation.nextScreen(
                          context, EditTimeSheetScreen(dayTitle: dayNameTitle, selectedYear: selectedYear));
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: AppSize.h10),
                      child: Image.asset(
                        AssetsPath.editImage,
                        height: AppSize.w28,
                        width: AppSize.w28,
                      ),
                    ),
                  ),
                )
              : SizedBox()
        ],
      ),
      body: Column(
        children: [
          dayData.length == 0
              ? Column(
                  children: [
                    Container(
                      height: AppSize.h42,
                      color: context.themeColors.listGridColor1,
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w14),
                      alignment: Alignment.centerLeft,
                      child: Text('',
                          style: context.textTheme.bodyMedium?.copyWith(
                            color: context.themeColors.textColor,
                            fontSize: AppSize.sp15,
                          )),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                      alignment: Alignment.centerLeft,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SpaceV(AppSize.h16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.from,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp11,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text('',
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.until,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp11,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text('',
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.breakText,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp11,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text('',
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.total,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp10,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text("Invalid Date",
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                            ],
                          ),
                          SpaceV(AppSize.h12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(AppLocalizations.of(context)!.activities,
                                  style: context.textTheme.titleMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp11,
                                  )),
                              SpaceV(AppSize.h2),
                              Text('',
                                  style: context.textTheme.titleMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp14,
                                  )),
                            ],
                          ),
                          SpaceV(AppSize.h12),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(AppLocalizations.of(context)!.remark,
                                  style: context.textTheme.titleMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp11,
                                  )),
                              SpaceV(AppSize.h2),
                              Text("-",
                                  style: context.textTheme.titleMedium?.copyWith(
                                    color: context.themeColors.textColor,
                                    fontSize: AppSize.sp14,
                                  )),
                            ],
                          ),
                          SpaceV(AppSize.h20),
                        ],
                      ),
                    ),
                  ],
                )
              : Expanded(
                  child: ListView.builder(
                  itemCount: dayData.length,
                  scrollDirection: Axis.vertical,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Container(
                          height: AppSize.h42,
                          color: context.themeColors.listGridColor1,
                          padding: EdgeInsets.symmetric(horizontal: AppSize.w14),
                          alignment: Alignment.centerLeft,
                          child: Text('${dayData[index].department}',
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.themeColors.textColor,
                                fontSize: AppSize.sp15,
                              )),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: AppSize.w16),
                          alignment: Alignment.centerLeft,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SpaceV(AppSize.h16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(AppLocalizations.of(context)!.from,
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp11,
                                          )),
                                      SpaceV(AppSize.h2),
                                      Text('${dayData[index].timeFrom}',
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp14,
                                          )),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(AppLocalizations.of(context)!.until,
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp11,
                                          )),
                                      SpaceV(AppSize.h2),
                                      Text('${dayData[index].timeUntil}',
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp14,
                                          )),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(AppLocalizations.of(context)!.breakText,
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp11,
                                          )),
                                      SpaceV(AppSize.h2),
                                      Text('${dayData[index].breakTime ?? "-"}',
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp14,
                                          )),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(AppLocalizations.of(context)!.total,
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp10,
                                          )),
                                      SpaceV(AppSize.h2),
                                      Text(
                                          "${hourBloc.calculateTotalHours(date: dayData[index].date, timeFrom: dayData[index].timeFrom ?? "00:00", timeUntil: dayData[index].timeUntil ?? "00:00", breakTime: dayData[index].breakTime ?? "00:00")}",
                                          style: context.textTheme.titleMedium?.copyWith(
                                            color: context.themeColors.textColor,
                                            fontSize: AppSize.sp14,
                                          )),
                                    ],
                                  ),
                                ],
                              ),
                              SpaceV(AppSize.h12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.activities,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp11,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text('${dayData[index].costCenters ?? "-"}',
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                              SpaceV(AppSize.h12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(AppLocalizations.of(context)!.remark,
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp11,
                                      )),
                                  SpaceV(AppSize.h2),
                                  Text("${(dayData[index].remark)!.isEmpty ? "-" : "${dayData[index].remark}"}",
                                      style: context.textTheme.titleMedium?.copyWith(
                                        color: context.themeColors.textColor,
                                        fontSize: AppSize.sp14,
                                      )),
                                ],
                              ),
                              SpaceV(AppSize.h20),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ))
        ],
      ),
    );
  }
}
