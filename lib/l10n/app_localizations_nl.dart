// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Dutch Flemish (`nl`).
class AppLocalizationsNl extends AppLocalizations {
  AppLocalizationsNl([String locale = 'nl']) : super(locale);

  @override
  String get englishLanguage => 'English';

  @override
  String get dutchLanguage => 'Nederlands';

  @override
  String get title => 'Staff Medewerker';

  @override
  String get screen1 => '---> Welcome Screen <---';

  @override
  String get welcomeToStaff => 'Welkom bij Staff!';

  @override
  String get knowDifferentName =>
      'Misschien ken je ons onder een van onze labels:';

  @override
  String get oneTimeActivation =>
      'Deze app vereist een eenmalige registratie voordat deze te gebruiken is.';

  @override
  String get easyQuick => 'Maar geen zorgen, het is snel én simpel.';

  @override
  String get activate => 'Activeren';

  @override
  String get switchToEnglish => 'Switch to english';

  @override
  String get screen2 => '---> Onboarding Screen <---';

  @override
  String get appActivation => 'App activeren';

  @override
  String get appActivationDetailText =>
      'Voor deze registratie gebruik je de QR code uit de welkomstmail. Ook kun je deze code zien op de Mijn Profiel pagina als je inlogt met een laptop of pc.';

  @override
  String get appActivationStep1 => '1. Log in zoals je gewend bent';

  @override
  String get appActivationStep2 => '2. Ga naar \'Mijn profiel\'';

  @override
  String get appActivationStep3 => '3. Scan de QR code of kopieer de link';

  @override
  String get swipeLeftToContinue => 'swipe naar links om verder te gaan';

  @override
  String get scanCode => 'Scan de code';

  @override
  String get pasteLink => 'Plak hier de link';

  @override
  String get or => 'Of';

  @override
  String get scannerScreenText => 'Scan de QR code op de Mijin Profiel pagina';

  @override
  String get pleaseWait => 'Een moment geduld';

  @override
  String get invalidLink => 'Invalid link.';

  @override
  String get login_screen => '------> login screen text  <------';

  @override
  String get userName => 'Gebruikersnaam';

  @override
  String get passWord => 'Wachtwoord';

  @override
  String get signIn => 'Inloggen';

  @override
  String get scanNewCode => 'Nieuwe code scannen';

  @override
  String get forgetPassword => 'Wachtwoord vergeten';

  @override
  String get enterUserNamePassword => 'Vul je gebruikersnaam en wachtwoord in';

  @override
  String get inCorrectUserNamePassword =>
      'Ongeldige gebruikersnaam of wachtwoord';

  @override
  String get invalidCredentialsText => 'Inloggevens incorrect';

  @override
  String get forget_password_screen =>
      '------> forget password screen text  <------';

  @override
  String get passwordReset => 'Wachtwoord vergeten';

  @override
  String get resetPassword => 'Wachtwoord resetten';

  @override
  String get forgetYourPassword => 'Je wachtwoord vergeten? geen probleem!';

  @override
  String get forgetPasswordText =>
      'Vul hieronder je gebruikersnaam en je ontvangt een email met verdere instructies om weer in je account te komen.';

  @override
  String get forgetErrorText =>
      'Er ging iets mis, probeer het later opnieuw of neem contact op met uw IT dienst.';

  @override
  String get availabilityAppbarText => 'Beschikbaarheid';

  @override
  String get scheduleAppbarText => 'Rooster';

  @override
  String get hoursAppbarText => 'Uren';

  @override
  String get newsAppbarText => 'Nieuws';

  @override
  String get okUnderstood => 'OK begrepen';

  @override
  String get drawerTexts => '------> Drawer Text  <------';

  @override
  String get home => 'Thuis';

  @override
  String get schedule => 'Rooster';

  @override
  String get hours => 'Uren';

  @override
  String get availability => 'Beschikbaarheid';

  @override
  String get myProfile => 'Mijn profiel';

  @override
  String get signout => 'Uitloggen';

  @override
  String get about => 'Over de app';

  @override
  String get news => 'Nieuws';

  @override
  String get absence => 'Verlof';

  @override
  String get payslips => 'Salarisstroken';

  @override
  String get settings => 'Instellingen';

  @override
  String get notifications => 'Notificaties';

  @override
  String get clocking => 'Klokken';

  @override
  String get openServices => 'Open diensten';

  @override
  String get noNewNotificationText => 'Geen nieuwe notificaties';

  @override
  String get poweredByStaffText => 'Powered by Staff Support';

  @override
  String get clocking_screen_text => '------> Clocking screen text  <------';

  @override
  String get clockingText => 'Klokken';

  @override
  String get departmentText => 'Afdeling';

  @override
  String get activityText => 'Activiteit';

  @override
  String get clockInText => 'Inklokken';

  @override
  String get clockOutText => 'Uitklokken';

  @override
  String get clockedSinceText => 'Ingeklokt sinds: ';

  @override
  String get notClockedText => 'Je bent niet ingeklokt';

  @override
  String get noDepartmentSelected => 'Er is geen afdeling geselecteerd';

  @override
  String get noActivitySelectedText => 'Er is geen activiteit geselecteerd';

  @override
  String get errorText => 'Er ging iets mis';

  @override
  String get clockedInTime1 => 'Je bent al';

  @override
  String get clockedInTime2 => 'uur en';

  @override
  String get clockedInTime3 => 'minuten ingeklokt';

  @override
  String get clockingFailed =>
      'Inklokken mislukt. Je kunt alleen inklokken wanneer jouw apparaat verbonden is met toegestane (wifi)netwerken.';

  @override
  String get profile_screen_text => '------> Profile screen text  <------';

  @override
  String get profileText => 'Profiel';

  @override
  String get personalInformationText => 'Persoonlijke gegevens';

  @override
  String get contactInformationText => 'Contactgegevens';

  @override
  String get addressInformationText => 'Adresgegevens';

  @override
  String get changePasswordText => 'Wachtwoord wijzigen';

  @override
  String get deleteAccountText => 'Account verwijderen';

  @override
  String get deleteAccountDetailsText =>
      'Om uw account en gegevens te verwijderen mail naar: <EMAIL>';

  @override
  String get supportEmail => '<EMAIL>';

  @override
  String get optionText => 'Opties';

  @override
  String get cameraText => 'Foto maken';

  @override
  String get selectPhotoText => 'Foto selecteren';

  @override
  String get profile_information_screen_text =>
      '------> Profile information screen text  <------';

  @override
  String get firstNameText => 'Roepnaam';

  @override
  String get fullNameText => 'Volledige naam';

  @override
  String get dateOfBirthText => 'Geboortedatum';

  @override
  String get bankAccountText => 'Bankrekening';

  @override
  String get change_password_screen_text =>
      '------> Change Password screen text  <------';

  @override
  String get currentPasswordText => 'Huidig wachtwoord';

  @override
  String get newPasswordText => 'Nieuw wachtwoord';

  @override
  String get confirmNewPasswordText => 'Bevestig nieuw wachtwoord';

  @override
  String get saveButtonText => 'Opslaan';

  @override
  String get changePasswordText2 => 'Wachtwoord wijzigen';

  @override
  String get changePasswordInfoText =>
      'Dit wachtwoord is geldig voor jouw Staff account, hier wijzigen betekent dat je wachtwoord voor de web-applicatie ook verandert.';

  @override
  String get passwordCantEmptyText => 'Vul een suggestie of opmerking in';

  @override
  String get passwordNotMatchedText => 'Wachtwoorden komen niet overeen';

  @override
  String get enterPasswordErrorText => 'Voer wachtwoord in';

  @override
  String get passwordChangedText => 'Wachtwoord veranderd';

  @override
  String get closeText => 'Sluiten';

  @override
  String get payslip_screen_text => '------> Payslip screen text  <------';

  @override
  String get openingFileText => 'Bestand openen...';

  @override
  String get noDocumentAvailableText => 'Geen documenten beschikbaar';

  @override
  String get fileNotAvailableText => 'Document niet beschikbaar';

  @override
  String get home_screen_text => '------> Home screen text  <------';

  @override
  String get summaryWeek => 'Overzicht';

  @override
  String get upComingShift => 'Eerstvolgende dienst';

  @override
  String get noShiftAvailable => 'Geen dienst beschikbaar';

  @override
  String get straightTo => 'Direct naar';

  @override
  String get plannedShift => 'Geplande diensten';

  @override
  String get workHour => 'Gewerkte uren';

  @override
  String get latestNews => 'Laatste nieuws';

  @override
  String get noNewFound => 'Geen nieuws gevonden';

  @override
  String get setting_screen_text => '------> Setting screen text <------';

  @override
  String get generalText => 'Algemeen';

  @override
  String get languageText => 'Taal';

  @override
  String get cancelText => 'Annuleren';

  @override
  String get dashBoardModeText => 'Dashboard mode';

  @override
  String get hourModeText => 'Uren mode';

  @override
  String get weekText => 'Week';

  @override
  String get monthText => 'Maand';

  @override
  String get swipeDownOnDashboardText =>
      'Swipe omlaag op je dashboard om de verandering te zien';

  @override
  String get securityText => 'Beveiliging';

  @override
  String get amountOfNewsText => 'Aantal nieuws items';

  @override
  String get darkThemeText => 'Donker thema';

  @override
  String get privacyText => 'Privacy';

  @override
  String get shareAnalyticalDataText => 'Analytische gegevens delen';

  @override
  String get pin_screen_text => '------> Pin screen text <------';

  @override
  String get enterYourPinText => 'Enter your pin';

  @override
  String get usePinText => 'Pincode gebruiken';

  @override
  String get usePinHelpInfoText =>
      'Met een pincode zorg je ervoor dat niet iedereen de app kan openen en bescherm je jouw gegevens.';

  @override
  String get disablePinText => 'Pincode uitschakelen';

  @override
  String get disablePinMessageText => 'Weet je zeker dat je dit wilt doen?';

  @override
  String get enterPinFirstTimeText => 'Voer een pincode in';

  @override
  String get enterPinSecondTimeText => 'Herhaal de pincode in';

  @override
  String get pinSetText => 'Pincode ingesteld';

  @override
  String get pinSetMessageText =>
      'Je kan voortaan in de app inloggen met je pincode. Je kan deze altijd uitzetten in de instellingen.';

  @override
  String get pinNotMatchText => 'Pincode niet hetzelfde';

  @override
  String get errorPinNotMatchText =>
      'De ingevoerde pincodes komen niet overheen. probeer het opnieuw.';

  @override
  String get pinIncorrectText => 'Pincode onjuist';

  @override
  String get pinIncorrectMessageText => 'De ingevoerde pincode is onjuist.';

  @override
  String get forgotPinText => 'Pincode vergeten?';

  @override
  String get forgotPinInfoText =>
      'Je pincode kan je resetten door opnieuw in te loggen met je gebruikersnaam en wachtwoord.';

  @override
  String get resetPinText => 'Resetten pincode';

  @override
  String get pinResetText => 'Pincode resetten';

  @override
  String get pinResetSuccessText =>
      'De pincode is gereset. Je kan een nieuwe pincode instellen bij de instellingen.';

  @override
  String get availability_screen_text =>
      '------> availability screen text  <------';

  @override
  String get saved => 'Gegevens opgeslagen';

  @override
  String get availabilityErrorText =>
      'Er ging iets mis, meld dit aan je systeembeheer';

  @override
  String get remarkDot => 'Opmerking:';

  @override
  String get shifts => 'Diensten';

  @override
  String get availabilityDot => 'Beschikbaarheid:';

  @override
  String get startTime => 'Starttijd:';

  @override
  String get endTime => 'Eindtijd:';

  @override
  String get available => 'Beschikbaar';

  @override
  String get unavailable => 'Onbeschikbaar';

  @override
  String get weekInformation => 'Week informatie';

  @override
  String get availabilityClosed => 'Beschikbaarheid opgeven niet mogelijk';

  @override
  String get allAvailable => 'Beschikbaar markeren';

  @override
  String get allUnAvailable => 'Onbeschikbaar markeren';

  @override
  String get from1 => 'From';

  @override
  String get untill1 => 'Untill';

  @override
  String get oK => 'OK';

  @override
  String get hour_screen_text => '------> hour screen text  <------';

  @override
  String get from => 'VAN';

  @override
  String get until => 'TOT';

  @override
  String get breakText => 'PAUZE';

  @override
  String get total => 'TOTAAL';

  @override
  String get department => 'Afdeling';

  @override
  String get task => 'Opdracht';

  @override
  String get selectedTask => 'Selecteer opdracht...';

  @override
  String get activities => 'Activiteiten';

  @override
  String get timeSheet => 'Tijdregistraties';

  @override
  String get addTimeSheet => 'Registratie toevoegen';

  @override
  String get noTimeSheet => 'Er zijn nog geen tijden geregistreerd';

  @override
  String get sheetNotSaved => 'Registraties met een * zijn nog niet opgeslagen';

  @override
  String get date => 'Datum';

  @override
  String get closed => 'Gesloten';

  @override
  String get delete => 'Verwijderen';

  @override
  String get deleteTimeSheet => 'Tijdregistratie verwijderen?';

  @override
  String get deleteConfirmText =>
      'Weet u zeker dat u deze tijdregistratie wilt verwijderen?\nU kunt deze actie niet ongedaan maken.';

  @override
  String get timeSheetRemove => 'Tijdregistratie verwijderd!';

  @override
  String get saving => 'Opslaan';

  @override
  String get selectActivity => 'Selecteer activiteit...';

  @override
  String get remarkRequiredError => 'Opmerking is vereist voor deze activiteit';

  @override
  String get additional => 'Overige';

  @override
  String get lunch => 'Lunch';

  @override
  String get meal => 'Maaltijd';

  @override
  String get surChargeService => 'Toeslagdienst 10%';

  @override
  String get workingFromHome => 'Thuiswerken';

  @override
  String get remark => 'Opmerking';

  @override
  String get timeNotSet => 'Tijden niet correct ingevuld';

  @override
  String get savedSuccessful => 'Gegevens opgeslagen';

  @override
  String get breakNotSelectable =>
      'Je kan geen pauze selecteren omdat je afdeling deze vooraf heeft ingesteld';

  @override
  String get timeSheetFormError =>
      'De gewijzigde gegevens zijn niet correct! \n Wilt u deze s.v.p. eerst corrigeren?';

  @override
  String get januaryText => 'Januari';

  @override
  String get februaryText => 'Februari';

  @override
  String get marchText => 'Maart';

  @override
  String get aprilText => 'April';

  @override
  String get mayText => 'Mei';

  @override
  String get juneText => 'Juni';

  @override
  String get julyText => 'Juli';

  @override
  String get augustText => 'Augustus';

  @override
  String get septemberText => 'September';

  @override
  String get octoberText => 'Oktober';

  @override
  String get novemberText => 'November';

  @override
  String get decemberText => 'December';

  @override
  String get absence_screen_text => '------> absence screen text  <------';

  @override
  String get dateText => 'Date';

  @override
  String get beginText => 'Begin';

  @override
  String get structureText => 'Opbouw';

  @override
  String get withdrawalText => 'Opname';

  @override
  String get balanceText => 'Saldo';

  @override
  String get totalText => 'Totaal';

  @override
  String get vacationText => 'Vakantie';

  @override
  String get myRequestText => 'Mijn aanvragen';

  @override
  String get createNewRequestText => 'Nieuwe aanvraag maken';

  @override
  String get saldoText => 'Saldo';

  @override
  String get noDataFound => 'Geen data gevonden!';

  @override
  String get leaveAdmissionsText => 'Verlofopnames';

  @override
  String get newLeaveRequestText => 'Nieuwe verlofaanvraag';

  @override
  String get requestLeaveText => 'Verlof aanvragen';

  @override
  String get startDateText => 'Start datum';

  @override
  String get endDateText => 'Eind datum';

  @override
  String get leaveTypeText => 'Verlof type';

  @override
  String get workedHoursText => 'Opgenomen uren';

  @override
  String get selectLeaveTypeText => 'Selecteer verlof type';

  @override
  String get leaveReasonText => 'Reden voor verlof';

  @override
  String get submitLeaveText => 'Verlof indienen';

  @override
  String get someFieldsEmptyText => 'Niet alle velden zijn ingevuld';

  @override
  String get leaveReqSubmittedText => 'Verlof aanvraag ingediend';

  @override
  String get periodText => 'Periode';

  @override
  String get selectPeriodText => 'Selecteer een periode';

  @override
  String get leaveErrorMessage => 'Er ging iets mis, probeer het later opnieuw';

  @override
  String get about_screen_text => '------> about screen text  <------';

  @override
  String get sendFeedbackText => 'Send feedback';

  @override
  String get problemExplanationText => 'Problem explanation';

  @override
  String get titleOrSubjectText => 'Title or subject';

  @override
  String get attachAnonymousDeviceInfo =>
      'Attach anonymous device info (optional)';

  @override
  String get doYouWantToReportText =>
      'Wil je een probleem melden over deze app? Omschrijf dan zo duidelijk mogelijk wat er niet goed gaat en voeg eventueel een screenshot toe, zodat onze support jou zo snel mogelijk kan helpen.';

  @override
  String get attachImageText => 'afbeelding toevoegen';

  @override
  String get attachmentText => 'Schermafbeelding';

  @override
  String get appVersionLabel => 'App versie';

  @override
  String get privacyPolicyLabel => 'Privacy policy';

  @override
  String get disclaimerLabel => 'Disclaimer';

  @override
  String get generalInformationLabel =>
      'De Staff medewerker app maakt het mogelijk voor werknemers en werkgevers om op een snelle en toegankelijke manier bij belangrijke informatie te komen.\n  Zo zijn er overzichten voor het rooster, uren, persoonlijke informatie en nog meer.';

  @override
  String get webAppInformationLabel =>
      'De mobiele applicatie bevat niet dezelfde functionaliteit als de web applicatie en is daarom een aanvulling op het pakket dat Staff u biedt.';

  @override
  String get betaStateInformationLabel =>
      'De staff medewerker app verkeerd op het moment in alpha staat. Functionaliteit is mogelijk incompleet, niet werkend en kan ten alle tijde veranderen.';

  @override
  String get thirdPartySoftwareLabel => 'Third-party software';

  @override
  String get feedbackSuccessMessage => 'Bedankt voor de feedback!';

  @override
  String get feedbackErrorMessage =>
      'Er ging iets mis, probeer het later opnieuw';

  @override
  String get titleText => 'Third-party software';

  @override
  String get generalInformationText =>
      'De Staff app maakt gebruik van een aantal software componenten die vereisen dat we een licentie tonen binnen onze applicatie. Hieronder vindt u deze licenties';

  @override
  String get attachPersonalInfoLabel =>
      'Persoonlijke informatie meesturen zodat Staff mogelijk contact kan opnemen. (optioneel)';

  @override
  String get sendFeedbackButtonLabel => 'Feedback versturen';

  @override
  String get sendButtonLabel => 'Verstuur';

  @override
  String get licenseText =>
      'The MIT License (MIT)\n\nCopyright (c) 2023\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.';

  @override
  String get schedule_screen_text => '------> schedule screen text  <------';

  @override
  String get today => 'Vandaag';

  @override
  String get day => 'Dag';

  @override
  String get week => 'Week';

  @override
  String get month => 'Maand';

  @override
  String get calendar => 'Kalender';

  @override
  String get downloadSchedule => 'Rooster downloaden';

  @override
  String get departmentSchedule => 'Dagrooster bekijken';

  @override
  String get noShifts => 'Geen diensten';

  @override
  String get download => 'Downloaden';

  @override
  String get refresh => 'Verversen';

  @override
  String get startScheduleTime => 'Begintijd';

  @override
  String get breakScheduleText => 'Pauze';

  @override
  String get scheduleShift => 'Shift';

  @override
  String get dayRemark => 'Dag opmerking';

  @override
  String get remarkFromCollege => 'Opmerking van collega';

  @override
  String get remarkFromManager => 'Opmerking van manager';

  @override
  String get assignment => 'Opdracht';

  @override
  String get rosterGroup => 'Roostergroep';

  @override
  String get costCenter => 'Activiteiten';

  @override
  String get service => 'Dienst';

  @override
  String get noDepartment => 'Geen afdeling';

  @override
  String get noScheduleAvailable => 'Er is geen rooster beschikbaar';

  @override
  String get reflectChanges =>
      'Swipe omlaag op je dashboard om de verandering te zien';

  @override
  String get noThanks => 'NEE DANKJE';

  @override
  String get setPin => 'PIN INSTELLEN';

  @override
  String get setPinCode => 'Pincode instellen';

  @override
  String get setPinMsg =>
      'Om je gegevens te beschermen raden we je aan om een pincode in te stellen.\nJe kan later altijd nog een pin instellen in de instellingen.';

  @override
  String get openFile => 'Bestand openen...';

  @override
  String get thankFeedBack => 'Bedankt voor de feedback!';

  @override
  String get thankFeedBackError =>
      'Er ging iets mis, probeer het later opnieuw';

  @override
  String get subscribe => 'Inschrijven';

  @override
  String get unsubscribe => 'Uitschrijven';

  @override
  String get subscribeSuccess => 'Inschrijven gelukt';

  @override
  String get unsubscribeSuccess => 'Uitschrijven gelukt';

  @override
  String get swap_text => '------> swap text  <------';

  @override
  String get openShift => 'Open diensten';

  @override
  String get swapShift => 'Ruilen';

  @override
  String get cancelSwap => 'Ruilen intrekken';

  @override
  String get requestToSwap => 'Verzoek tot ruilen is ingediend';

  @override
  String get swapWithdrawn => 'Ruilverzoek ingetrokken';

  @override
  String get refuse => 'Weigeren';

  @override
  String get registerShift => 'Opmerking van collega';

  @override
  String get managerRemark => 'Opmerking van manager';

  @override
  String get registeredOnShift => 'Je bent ingeschreven op de dienst';

  @override
  String get swapRequestRefused => 'Ruilverzoek geweigerd';

  @override
  String get youHaveRegistered => 'Je bent ingeschreven op deze dienst';

  @override
  String get youAreAboutSwapShift =>
      'Je staat op het punt deze dienst te ruilen';

  @override
  String get noteForColleagues =>
      'Schrijf hier een opmerking voor je collega\'s';

  @override
  String get noCancel => 'Nee, terug';

  @override
  String get yesSwapShift => 'Ja, ruilen';

  @override
  String get monday => 'maandag';

  @override
  String get tuesday => 'dinsdag';

  @override
  String get wednesday => 'woensdag';

  @override
  String get thursday => 'donderdag';

  @override
  String get friday => 'vrijdag';

  @override
  String get saturday => 'zaterdag';

  @override
  String get sunday => 'zondag';

  @override
  String get january => 'januari';

  @override
  String get february => 'februari';

  @override
  String get march => 'maart';

  @override
  String get april => 'april';

  @override
  String get may => 'mei';

  @override
  String get june => 'juni';

  @override
  String get july => 'juli';

  @override
  String get august => 'augustus';

  @override
  String get september => 'september';

  @override
  String get october => 'oktober';

  @override
  String get november => 'november';

  @override
  String get december => 'december';

  @override
  String get leveRequest => 'Verlof verzoek';

  @override
  String get start => 'Begin';

  @override
  String get end => 'Einde';

  @override
  String get submittedOn => 'Aangevraagd op';

  @override
  String get leaveType => 'Soort verlof';

  @override
  String get state => 'Toestand';

  @override
  String get requestedHours => 'Uren aagevraagd';

  @override
  String get registeredHours => 'Uren geregistreerd';

  @override
  String get reason => 'Reden';

  @override
  String get declaration => 'Declaratie';

  @override
  String get declarationsNotFound => 'Geen declaraties gevonden!';

  @override
  String get overview => 'Overzicht';

  @override
  String get kilometers => 'Kilometers';

  @override
  String get bon => 'Bon';

  @override
  String get submitVoucher => 'Nieuwe declaratie';

  @override
  String get takePhoto => 'Neem foto';

  @override
  String get browsingPhone => 'Selecteer uit galerij';

  @override
  String get mileageRegistration => 'Nieuwe reiskostendeclaratie';

  @override
  String get newReceipt => 'Nieuwe bon';

  @override
  String get nameBean => 'Naam van de bon';

  @override
  String get receiptSummary => 'Bon samenvatting';

  @override
  String get totalAmount => 'Totaal bedrag';

  @override
  String get receiptDate => 'Datum bon';

  @override
  String get description => 'Omschrijving';

  @override
  String get saveReceipt => 'Declaratie indienen';

  @override
  String get confirm => 'Bevestigen';

  @override
  String get confirmDeclaration => 'Bevestig declaratie';

  @override
  String get notundoneDeclration =>
      'Als je deze declaratie hebt ingeleverd kan dit niet meer ongedaan gemaakt worden.';

  @override
  String get goBack => 'Ga terug';

  @override
  String get declrationSave => 'Opslaan';

  @override
  String get voucherSucessFully => 'Bon succesvol opgeslagen';

  @override
  String get numberofKiometers => 'Aantal kilometers';

  @override
  String get cost => 'Kosten';

  @override
  String get dateVoucher => 'Datum bon';

  @override
  String get saveRegistration => 'Declaratie indienen';

  @override
  String get enterKilometers => 'Voer kilometers in';

  @override
  String get enterTotalAmount => 'Vul het totaalbedrag in';

  @override
  String get selectDate => 'Selecteer Datum';

  @override
  String get enterDescription => 'Voer een beschrijving in';

  @override
  String get ipAuthoriseError =>
      'Inloggen is niet geoorloofd vanaf dit apparaat';

  @override
  String get enterNameofBon => 'Vul de naam van de Bon in';
}
