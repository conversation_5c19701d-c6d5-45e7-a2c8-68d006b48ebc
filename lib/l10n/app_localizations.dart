import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_nl.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('nl'),
  ];

  /// No description provided for @englishLanguage.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get englishLanguage;

  /// No description provided for @dutchLanguage.
  ///
  /// In en, this message translates to:
  /// **'Nederlands'**
  String get dutchLanguage;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Staff employee app'**
  String get title;

  /// No description provided for @screen1.
  ///
  /// In en, this message translates to:
  /// **'---> Welcome Screen <---'**
  String get screen1;

  /// No description provided for @welcomeToStaff.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Staff!'**
  String get welcomeToStaff;

  /// No description provided for @knowDifferentName.
  ///
  /// In en, this message translates to:
  /// **'Maybe you know us under a different name:'**
  String get knowDifferentName;

  /// No description provided for @oneTimeActivation.
  ///
  /// In en, this message translates to:
  /// **'This app requires one-time activation before it can be used.'**
  String get oneTimeActivation;

  /// No description provided for @easyQuick.
  ///
  /// In en, this message translates to:
  /// **'But don\'t worry, it\'s easy and quick.'**
  String get easyQuick;

  /// No description provided for @activate.
  ///
  /// In en, this message translates to:
  /// **'Activate'**
  String get activate;

  /// No description provided for @switchToEnglish.
  ///
  /// In en, this message translates to:
  /// **'Switch to english'**
  String get switchToEnglish;

  /// No description provided for @screen2.
  ///
  /// In en, this message translates to:
  /// **'---> Onboarding Screen <---'**
  String get screen2;

  /// No description provided for @appActivation.
  ///
  /// In en, this message translates to:
  /// **'App activation'**
  String get appActivation;

  /// No description provided for @appActivationDetailText.
  ///
  /// In en, this message translates to:
  /// **'You can use the QR code that was sent to you by email to activate the app. You can also view this QR code on the \'Mijn profiel\' page on your PC.'**
  String get appActivationDetailText;

  /// No description provided for @appActivationStep1.
  ///
  /// In en, this message translates to:
  /// **'1. Log in as you normally would'**
  String get appActivationStep1;

  /// No description provided for @appActivationStep2.
  ///
  /// In en, this message translates to:
  /// **'2. Go to \'Mijn profiel\''**
  String get appActivationStep2;

  /// No description provided for @appActivationStep3.
  ///
  /// In en, this message translates to:
  /// **'3. Scan the QR-code or copy the url'**
  String get appActivationStep3;

  /// No description provided for @swipeLeftToContinue.
  ///
  /// In en, this message translates to:
  /// **'swipe left to continue'**
  String get swipeLeftToContinue;

  /// No description provided for @scanCode.
  ///
  /// In en, this message translates to:
  /// **'Scan the code'**
  String get scanCode;

  /// No description provided for @pasteLink.
  ///
  /// In en, this message translates to:
  /// **'Paste the link here'**
  String get pasteLink;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'Or'**
  String get or;

  /// No description provided for @scannerScreenText.
  ///
  /// In en, this message translates to:
  /// **'Scan de QR code op de Mijin Profiel pagina'**
  String get scannerScreenText;

  /// No description provided for @pleaseWait.
  ///
  /// In en, this message translates to:
  /// **'Please wait'**
  String get pleaseWait;

  /// No description provided for @invalidLink.
  ///
  /// In en, this message translates to:
  /// **'Invalid link.'**
  String get invalidLink;

  /// No description provided for @login_screen.
  ///
  /// In en, this message translates to:
  /// **'------> login screen text  <------'**
  String get login_screen;

  /// No description provided for @userName.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get userName;

  /// No description provided for @passWord.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passWord;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'SIGN IN'**
  String get signIn;

  /// No description provided for @scanNewCode.
  ///
  /// In en, this message translates to:
  /// **'Scan new code'**
  String get scanNewCode;

  /// No description provided for @forgetPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot password'**
  String get forgetPassword;

  /// No description provided for @enterUserNamePassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your username and password'**
  String get enterUserNamePassword;

  /// No description provided for @inCorrectUserNamePassword.
  ///
  /// In en, this message translates to:
  /// **'Incorrect username or password'**
  String get inCorrectUserNamePassword;

  /// No description provided for @invalidCredentialsText.
  ///
  /// In en, this message translates to:
  /// **'Invalid credentials'**
  String get invalidCredentialsText;

  /// No description provided for @forget_password_screen.
  ///
  /// In en, this message translates to:
  /// **'------> forget password screen text  <------'**
  String get forget_password_screen;

  /// No description provided for @passwordReset.
  ///
  /// In en, this message translates to:
  /// **'Password reset'**
  String get passwordReset;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset password'**
  String get resetPassword;

  /// No description provided for @forgetYourPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot your password? no problem!'**
  String get forgetYourPassword;

  /// No description provided for @forgetPasswordText.
  ///
  /// In en, this message translates to:
  /// **'Fill in your username below and you\'ll receive an email with instructions on how to get back into your account.'**
  String get forgetPasswordText;

  /// No description provided for @forgetErrorText.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong, please try again later or contact your IT support staff..'**
  String get forgetErrorText;

  /// No description provided for @availabilityAppbarText.
  ///
  /// In en, this message translates to:
  /// **'Availability'**
  String get availabilityAppbarText;

  /// No description provided for @scheduleAppbarText.
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get scheduleAppbarText;

  /// No description provided for @hoursAppbarText.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hoursAppbarText;

  /// No description provided for @newsAppbarText.
  ///
  /// In en, this message translates to:
  /// **'News'**
  String get newsAppbarText;

  /// No description provided for @okUnderstood.
  ///
  /// In en, this message translates to:
  /// **'OK understood'**
  String get okUnderstood;

  /// No description provided for @drawerTexts.
  ///
  /// In en, this message translates to:
  /// **'------> Drawer Text  <------'**
  String get drawerTexts;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @schedule.
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// No description provided for @hours.
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// No description provided for @availability.
  ///
  /// In en, this message translates to:
  /// **'Availability'**
  String get availability;

  /// No description provided for @myProfile.
  ///
  /// In en, this message translates to:
  /// **'My profile'**
  String get myProfile;

  /// No description provided for @signout.
  ///
  /// In en, this message translates to:
  /// **'Sign out'**
  String get signout;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About the app'**
  String get about;

  /// No description provided for @news.
  ///
  /// In en, this message translates to:
  /// **'News'**
  String get news;

  /// No description provided for @absence.
  ///
  /// In en, this message translates to:
  /// **'Absence'**
  String get absence;

  /// No description provided for @payslips.
  ///
  /// In en, this message translates to:
  /// **'Payslips'**
  String get payslips;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @clocking.
  ///
  /// In en, this message translates to:
  /// **'Clocking'**
  String get clocking;

  /// No description provided for @openServices.
  ///
  /// In en, this message translates to:
  /// **'Open services'**
  String get openServices;

  /// No description provided for @noNewNotificationText.
  ///
  /// In en, this message translates to:
  /// **'No new notifications'**
  String get noNewNotificationText;

  /// No description provided for @poweredByStaffText.
  ///
  /// In en, this message translates to:
  /// **'Powered by Staff Support'**
  String get poweredByStaffText;

  /// No description provided for @clocking_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Clocking screen text  <------'**
  String get clocking_screen_text;

  /// No description provided for @clockingText.
  ///
  /// In en, this message translates to:
  /// **'Clocking'**
  String get clockingText;

  /// No description provided for @departmentText.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get departmentText;

  /// No description provided for @activityText.
  ///
  /// In en, this message translates to:
  /// **'Activity'**
  String get activityText;

  /// No description provided for @clockInText.
  ///
  /// In en, this message translates to:
  /// **'Clock in'**
  String get clockInText;

  /// No description provided for @clockOutText.
  ///
  /// In en, this message translates to:
  /// **'Clock out'**
  String get clockOutText;

  /// No description provided for @clockedSinceText.
  ///
  /// In en, this message translates to:
  /// **'clocked in since: '**
  String get clockedSinceText;

  /// No description provided for @notClockedText.
  ///
  /// In en, this message translates to:
  /// **'You are not clocked in'**
  String get notClockedText;

  /// No description provided for @noDepartmentSelected.
  ///
  /// In en, this message translates to:
  /// **'No department has been selected'**
  String get noDepartmentSelected;

  /// No description provided for @noActivitySelectedText.
  ///
  /// In en, this message translates to:
  /// **'No activity has been selected'**
  String get noActivitySelectedText;

  /// No description provided for @errorText.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong'**
  String get errorText;

  /// No description provided for @clockedInTime1.
  ///
  /// In en, this message translates to:
  /// **'You\'ve been clocked in for'**
  String get clockedInTime1;

  /// No description provided for @clockedInTime2.
  ///
  /// In en, this message translates to:
  /// **'hours and'**
  String get clockedInTime2;

  /// No description provided for @clockedInTime3.
  ///
  /// In en, this message translates to:
  /// **'minutes'**
  String get clockedInTime3;

  /// No description provided for @clockingFailed.
  ///
  /// In en, this message translates to:
  /// **'Clocking in failed. You can only clock in when your device is connected to a whitelisted (wifi) network.'**
  String get clockingFailed;

  /// No description provided for @profile_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Profile screen text  <------'**
  String get profile_screen_text;

  /// No description provided for @profileText.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileText;

  /// No description provided for @personalInformationText.
  ///
  /// In en, this message translates to:
  /// **'Personal information'**
  String get personalInformationText;

  /// No description provided for @contactInformationText.
  ///
  /// In en, this message translates to:
  /// **'Contact information'**
  String get contactInformationText;

  /// No description provided for @addressInformationText.
  ///
  /// In en, this message translates to:
  /// **'Address information'**
  String get addressInformationText;

  /// No description provided for @changePasswordText.
  ///
  /// In en, this message translates to:
  /// **'Change password'**
  String get changePasswordText;

  /// No description provided for @deleteAccountText.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccountText;

  /// No description provided for @deleteAccountDetailsText.
  ///
  /// In en, this message translates to:
  /// **'To delete your account and data mail to: <EMAIL>'**
  String get deleteAccountDetailsText;

  /// No description provided for @supportEmail.
  ///
  /// In en, this message translates to:
  /// **'<EMAIL>'**
  String get supportEmail;

  /// No description provided for @optionText.
  ///
  /// In en, this message translates to:
  /// **'Options'**
  String get optionText;

  /// No description provided for @cameraText.
  ///
  /// In en, this message translates to:
  /// **'Take photo'**
  String get cameraText;

  /// No description provided for @selectPhotoText.
  ///
  /// In en, this message translates to:
  /// **'Select photo'**
  String get selectPhotoText;

  /// No description provided for @profile_information_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Profile information screen text  <------'**
  String get profile_information_screen_text;

  /// No description provided for @firstNameText.
  ///
  /// In en, this message translates to:
  /// **'First name'**
  String get firstNameText;

  /// No description provided for @fullNameText.
  ///
  /// In en, this message translates to:
  /// **'Full name'**
  String get fullNameText;

  /// No description provided for @dateOfBirthText.
  ///
  /// In en, this message translates to:
  /// **'Date of birth'**
  String get dateOfBirthText;

  /// No description provided for @bankAccountText.
  ///
  /// In en, this message translates to:
  /// **'Bank account'**
  String get bankAccountText;

  /// No description provided for @change_password_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Change Password screen text  <------'**
  String get change_password_screen_text;

  /// No description provided for @currentPasswordText.
  ///
  /// In en, this message translates to:
  /// **'Current password'**
  String get currentPasswordText;

  /// No description provided for @newPasswordText.
  ///
  /// In en, this message translates to:
  /// **'New password'**
  String get newPasswordText;

  /// No description provided for @confirmNewPasswordText.
  ///
  /// In en, this message translates to:
  /// **'Confirm new password'**
  String get confirmNewPasswordText;

  /// No description provided for @saveButtonText.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get saveButtonText;

  /// No description provided for @changePasswordText2.
  ///
  /// In en, this message translates to:
  /// **'Change password'**
  String get changePasswordText2;

  /// No description provided for @changePasswordInfoText.
  ///
  /// In en, this message translates to:
  /// **'This password is valid for your Staff account, changing it here means changing your password for the web application as well.'**
  String get changePasswordInfoText;

  /// No description provided for @passwordCantEmptyText.
  ///
  /// In en, this message translates to:
  /// **'Suggestion or remark cannot be empty'**
  String get passwordCantEmptyText;

  /// No description provided for @passwordNotMatchedText.
  ///
  /// In en, this message translates to:
  /// **'Passwords don\'t match'**
  String get passwordNotMatchedText;

  /// No description provided for @enterPasswordErrorText.
  ///
  /// In en, this message translates to:
  /// **'Please enter password'**
  String get enterPasswordErrorText;

  /// No description provided for @passwordChangedText.
  ///
  /// In en, this message translates to:
  /// **'Password changed'**
  String get passwordChangedText;

  /// No description provided for @closeText.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get closeText;

  /// No description provided for @payslip_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Payslip screen text  <------'**
  String get payslip_screen_text;

  /// No description provided for @openingFileText.
  ///
  /// In en, this message translates to:
  /// **'Opening file...'**
  String get openingFileText;

  /// No description provided for @noDocumentAvailableText.
  ///
  /// In en, this message translates to:
  /// **'No documents available'**
  String get noDocumentAvailableText;

  /// No description provided for @fileNotAvailableText.
  ///
  /// In en, this message translates to:
  /// **'File not available'**
  String get fileNotAvailableText;

  /// No description provided for @home_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Home screen text  <------'**
  String get home_screen_text;

  /// No description provided for @summaryWeek.
  ///
  /// In en, this message translates to:
  /// **'Summary'**
  String get summaryWeek;

  /// No description provided for @upComingShift.
  ///
  /// In en, this message translates to:
  /// **'Upcoming shift'**
  String get upComingShift;

  /// No description provided for @noShiftAvailable.
  ///
  /// In en, this message translates to:
  /// **'No shift available'**
  String get noShiftAvailable;

  /// No description provided for @straightTo.
  ///
  /// In en, this message translates to:
  /// **'Straight to'**
  String get straightTo;

  /// No description provided for @plannedShift.
  ///
  /// In en, this message translates to:
  /// **'Planned shifts'**
  String get plannedShift;

  /// No description provided for @workHour.
  ///
  /// In en, this message translates to:
  /// **'Worked hours'**
  String get workHour;

  /// No description provided for @latestNews.
  ///
  /// In en, this message translates to:
  /// **'Latest news'**
  String get latestNews;

  /// No description provided for @noNewFound.
  ///
  /// In en, this message translates to:
  /// **'No News Found'**
  String get noNewFound;

  /// No description provided for @setting_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Setting screen text <------'**
  String get setting_screen_text;

  /// No description provided for @generalText.
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get generalText;

  /// No description provided for @languageText.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get languageText;

  /// No description provided for @cancelText.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancelText;

  /// No description provided for @dashBoardModeText.
  ///
  /// In en, this message translates to:
  /// **'Dashboard mode'**
  String get dashBoardModeText;

  /// No description provided for @hourModeText.
  ///
  /// In en, this message translates to:
  /// **'Hour mode'**
  String get hourModeText;

  /// No description provided for @weekText.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get weekText;

  /// No description provided for @monthText.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get monthText;

  /// No description provided for @swipeDownOnDashboardText.
  ///
  /// In en, this message translates to:
  /// **'Swipe down on the dashboard to reflect changes'**
  String get swipeDownOnDashboardText;

  /// No description provided for @securityText.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get securityText;

  /// No description provided for @amountOfNewsText.
  ///
  /// In en, this message translates to:
  /// **'Amount of news items'**
  String get amountOfNewsText;

  /// No description provided for @darkThemeText.
  ///
  /// In en, this message translates to:
  /// **'Dark theme'**
  String get darkThemeText;

  /// No description provided for @privacyText.
  ///
  /// In en, this message translates to:
  /// **'Privacy'**
  String get privacyText;

  /// No description provided for @shareAnalyticalDataText.
  ///
  /// In en, this message translates to:
  /// **'Share analytical data'**
  String get shareAnalyticalDataText;

  /// No description provided for @pin_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> Pin screen text <------'**
  String get pin_screen_text;

  /// No description provided for @enterYourPinText.
  ///
  /// In en, this message translates to:
  /// **'Enter your pin'**
  String get enterYourPinText;

  /// No description provided for @usePinText.
  ///
  /// In en, this message translates to:
  /// **'Use pin'**
  String get usePinText;

  /// No description provided for @usePinHelpInfoText.
  ///
  /// In en, this message translates to:
  /// **'With a pin you can restrict access to the app and secure your data.'**
  String get usePinHelpInfoText;

  /// No description provided for @disablePinText.
  ///
  /// In en, this message translates to:
  /// **'Disable pin'**
  String get disablePinText;

  /// No description provided for @disablePinMessageText.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to disable your pin?'**
  String get disablePinMessageText;

  /// No description provided for @enterPinFirstTimeText.
  ///
  /// In en, this message translates to:
  /// **'Enter a pin'**
  String get enterPinFirstTimeText;

  /// No description provided for @enterPinSecondTimeText.
  ///
  /// In en, this message translates to:
  /// **'Repeat the pin'**
  String get enterPinSecondTimeText;

  /// No description provided for @pinSetText.
  ///
  /// In en, this message translates to:
  /// **'Pin has been set'**
  String get pinSetText;

  /// No description provided for @pinSetMessageText.
  ///
  /// In en, this message translates to:
  /// **'You can now use this pin to sign into the app. You can always change this in the settings menu.'**
  String get pinSetMessageText;

  /// No description provided for @pinNotMatchText.
  ///
  /// In en, this message translates to:
  /// **'Pins do not match'**
  String get pinNotMatchText;

  /// No description provided for @errorPinNotMatchText.
  ///
  /// In en, this message translates to:
  /// **'The entered pins do not match. Please try again.'**
  String get errorPinNotMatchText;

  /// No description provided for @pinIncorrectText.
  ///
  /// In en, this message translates to:
  /// **'Pin incorrect'**
  String get pinIncorrectText;

  /// No description provided for @pinIncorrectMessageText.
  ///
  /// In en, this message translates to:
  /// **'The entered pin is incorrect.'**
  String get pinIncorrectMessageText;

  /// No description provided for @forgotPinText.
  ///
  /// In en, this message translates to:
  /// **'Forgot your pin?'**
  String get forgotPinText;

  /// No description provided for @forgotPinInfoText.
  ///
  /// In en, this message translates to:
  /// **'You can reset your pin by login in using your username and password.'**
  String get forgotPinInfoText;

  /// No description provided for @resetPinText.
  ///
  /// In en, this message translates to:
  /// **'Reset pin'**
  String get resetPinText;

  /// No description provided for @pinResetText.
  ///
  /// In en, this message translates to:
  /// **'Pin reset'**
  String get pinResetText;

  /// No description provided for @pinResetSuccessText.
  ///
  /// In en, this message translates to:
  /// **'The pin has been reset. You can set a new pin in the settings menu.'**
  String get pinResetSuccessText;

  /// No description provided for @availability_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> availability screen text  <------'**
  String get availability_screen_text;

  /// No description provided for @saved.
  ///
  /// In en, this message translates to:
  /// **'Saved'**
  String get saved;

  /// No description provided for @availabilityErrorText.
  ///
  /// In en, this message translates to:
  /// **'Somethig went wrong, please contact your IT service'**
  String get availabilityErrorText;

  /// No description provided for @remarkDot.
  ///
  /// In en, this message translates to:
  /// **'Remark:'**
  String get remarkDot;

  /// No description provided for @shifts.
  ///
  /// In en, this message translates to:
  /// **'Shifts'**
  String get shifts;

  /// No description provided for @availabilityDot.
  ///
  /// In en, this message translates to:
  /// **'Availability:'**
  String get availabilityDot;

  /// No description provided for @startTime.
  ///
  /// In en, this message translates to:
  /// **'Start time:'**
  String get startTime;

  /// No description provided for @endTime.
  ///
  /// In en, this message translates to:
  /// **'End time:'**
  String get endTime;

  /// No description provided for @available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// No description provided for @unavailable.
  ///
  /// In en, this message translates to:
  /// **'Unavailable'**
  String get unavailable;

  /// No description provided for @weekInformation.
  ///
  /// In en, this message translates to:
  /// **'Week information'**
  String get weekInformation;

  /// No description provided for @availabilityClosed.
  ///
  /// In en, this message translates to:
  /// **'Availability closed'**
  String get availabilityClosed;

  /// No description provided for @allAvailable.
  ///
  /// In en, this message translates to:
  /// **'All available'**
  String get allAvailable;

  /// No description provided for @allUnAvailable.
  ///
  /// In en, this message translates to:
  /// **'All unavailable'**
  String get allUnAvailable;

  /// No description provided for @from1.
  ///
  /// In en, this message translates to:
  /// **'From'**
  String get from1;

  /// No description provided for @untill1.
  ///
  /// In en, this message translates to:
  /// **'Untill'**
  String get untill1;

  /// No description provided for @oK.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get oK;

  /// No description provided for @hour_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> hour screen text  <------'**
  String get hour_screen_text;

  /// No description provided for @from.
  ///
  /// In en, this message translates to:
  /// **'FROM'**
  String get from;

  /// No description provided for @until.
  ///
  /// In en, this message translates to:
  /// **'UNTIL'**
  String get until;

  /// No description provided for @breakText.
  ///
  /// In en, this message translates to:
  /// **'BREAK'**
  String get breakText;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'TOTAL'**
  String get total;

  /// No description provided for @department.
  ///
  /// In en, this message translates to:
  /// **'Department'**
  String get department;

  /// No description provided for @task.
  ///
  /// In en, this message translates to:
  /// **'Task'**
  String get task;

  /// No description provided for @selectedTask.
  ///
  /// In en, this message translates to:
  /// **'Select task...'**
  String get selectedTask;

  /// No description provided for @activities.
  ///
  /// In en, this message translates to:
  /// **'Activities'**
  String get activities;

  /// No description provided for @timeSheet.
  ///
  /// In en, this message translates to:
  /// **'Time sheets'**
  String get timeSheet;

  /// No description provided for @addTimeSheet.
  ///
  /// In en, this message translates to:
  /// **'Add time sheet'**
  String get addTimeSheet;

  /// No description provided for @noTimeSheet.
  ///
  /// In en, this message translates to:
  /// **'No time sheets'**
  String get noTimeSheet;

  /// No description provided for @sheetNotSaved.
  ///
  /// In en, this message translates to:
  /// **'Sheets with a * have not been saved'**
  String get sheetNotSaved;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @closed.
  ///
  /// In en, this message translates to:
  /// **'Closed'**
  String get closed;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @deleteTimeSheet.
  ///
  /// In en, this message translates to:
  /// **'Delete time sheet?'**
  String get deleteTimeSheet;

  /// No description provided for @deleteConfirmText.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this time sheet? This cannot be undone.'**
  String get deleteConfirmText;

  /// No description provided for @timeSheetRemove.
  ///
  /// In en, this message translates to:
  /// **'Time sheet removed!'**
  String get timeSheetRemove;

  /// No description provided for @saving.
  ///
  /// In en, this message translates to:
  /// **'Saving'**
  String get saving;

  /// No description provided for @selectActivity.
  ///
  /// In en, this message translates to:
  /// **'Select an activity...'**
  String get selectActivity;

  /// No description provided for @remarkRequiredError.
  ///
  /// In en, this message translates to:
  /// **'Remark is required for this Activity'**
  String get remarkRequiredError;

  /// No description provided for @additional.
  ///
  /// In en, this message translates to:
  /// **'Additional'**
  String get additional;

  /// No description provided for @lunch.
  ///
  /// In en, this message translates to:
  /// **'Lunch'**
  String get lunch;

  /// No description provided for @meal.
  ///
  /// In en, this message translates to:
  /// **'Meal'**
  String get meal;

  /// No description provided for @surChargeService.
  ///
  /// In en, this message translates to:
  /// **'Surcharge service 10%'**
  String get surChargeService;

  /// No description provided for @workingFromHome.
  ///
  /// In en, this message translates to:
  /// **'Working from home'**
  String get workingFromHome;

  /// No description provided for @remark.
  ///
  /// In en, this message translates to:
  /// **'Remark'**
  String get remark;

  /// No description provided for @timeNotSet.
  ///
  /// In en, this message translates to:
  /// **'Times are not set'**
  String get timeNotSet;

  /// No description provided for @savedSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Save successful'**
  String get savedSuccessful;

  /// No description provided for @breakNotSelectable.
  ///
  /// In en, this message translates to:
  /// **'You cannot select a break because your department has set it in advance'**
  String get breakNotSelectable;

  /// No description provided for @timeSheetFormError.
  ///
  /// In en, this message translates to:
  /// **'The information is incorrect, would you please correct it?'**
  String get timeSheetFormError;

  /// No description provided for @januaryText.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get januaryText;

  /// No description provided for @februaryText.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get februaryText;

  /// No description provided for @marchText.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get marchText;

  /// No description provided for @aprilText.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get aprilText;

  /// No description provided for @mayText.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get mayText;

  /// No description provided for @juneText.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get juneText;

  /// No description provided for @julyText.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get julyText;

  /// No description provided for @augustText.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get augustText;

  /// No description provided for @septemberText.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get septemberText;

  /// No description provided for @octoberText.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get octoberText;

  /// No description provided for @novemberText.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get novemberText;

  /// No description provided for @decemberText.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get decemberText;

  /// No description provided for @absence_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> absence screen text  <------'**
  String get absence_screen_text;

  /// No description provided for @dateText.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get dateText;

  /// No description provided for @beginText.
  ///
  /// In en, this message translates to:
  /// **'Begin'**
  String get beginText;

  /// No description provided for @structureText.
  ///
  /// In en, this message translates to:
  /// **'Structure'**
  String get structureText;

  /// No description provided for @withdrawalText.
  ///
  /// In en, this message translates to:
  /// **'Withdrawal'**
  String get withdrawalText;

  /// No description provided for @balanceText.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balanceText;

  /// No description provided for @totalText.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get totalText;

  /// No description provided for @vacationText.
  ///
  /// In en, this message translates to:
  /// **'Vacation'**
  String get vacationText;

  /// No description provided for @myRequestText.
  ///
  /// In en, this message translates to:
  /// **'My Request'**
  String get myRequestText;

  /// No description provided for @createNewRequestText.
  ///
  /// In en, this message translates to:
  /// **'Create New Request'**
  String get createNewRequestText;

  /// No description provided for @saldoText.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get saldoText;

  /// No description provided for @noDataFound.
  ///
  /// In en, this message translates to:
  /// **'No data found!'**
  String get noDataFound;

  /// No description provided for @leaveAdmissionsText.
  ///
  /// In en, this message translates to:
  /// **'Leave admissions'**
  String get leaveAdmissionsText;

  /// No description provided for @newLeaveRequestText.
  ///
  /// In en, this message translates to:
  /// **'New leave request'**
  String get newLeaveRequestText;

  /// No description provided for @requestLeaveText.
  ///
  /// In en, this message translates to:
  /// **'Request leave'**
  String get requestLeaveText;

  /// No description provided for @startDateText.
  ///
  /// In en, this message translates to:
  /// **'Start date'**
  String get startDateText;

  /// No description provided for @endDateText.
  ///
  /// In en, this message translates to:
  /// **'End date'**
  String get endDateText;

  /// No description provided for @leaveTypeText.
  ///
  /// In en, this message translates to:
  /// **'Leave type'**
  String get leaveTypeText;

  /// No description provided for @workedHoursText.
  ///
  /// In en, this message translates to:
  /// **'Worked hours'**
  String get workedHoursText;

  /// No description provided for @selectLeaveTypeText.
  ///
  /// In en, this message translates to:
  /// **'Select leave type'**
  String get selectLeaveTypeText;

  /// No description provided for @leaveReasonText.
  ///
  /// In en, this message translates to:
  /// **'Leave reason'**
  String get leaveReasonText;

  /// No description provided for @submitLeaveText.
  ///
  /// In en, this message translates to:
  /// **'Submit leave'**
  String get submitLeaveText;

  /// No description provided for @someFieldsEmptyText.
  ///
  /// In en, this message translates to:
  /// **'Some fields are empty'**
  String get someFieldsEmptyText;

  /// No description provided for @leaveReqSubmittedText.
  ///
  /// In en, this message translates to:
  /// **'Leave request submitted'**
  String get leaveReqSubmittedText;

  /// No description provided for @periodText.
  ///
  /// In en, this message translates to:
  /// **'Period'**
  String get periodText;

  /// No description provided for @selectPeriodText.
  ///
  /// In en, this message translates to:
  /// **'Select a Period'**
  String get selectPeriodText;

  /// No description provided for @leaveErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong, try again later'**
  String get leaveErrorMessage;

  /// No description provided for @about_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> about screen text  <------'**
  String get about_screen_text;

  /// No description provided for @sendFeedbackText.
  ///
  /// In en, this message translates to:
  /// **'Send feedback'**
  String get sendFeedbackText;

  /// No description provided for @problemExplanationText.
  ///
  /// In en, this message translates to:
  /// **'Problem explanation'**
  String get problemExplanationText;

  /// No description provided for @titleOrSubjectText.
  ///
  /// In en, this message translates to:
  /// **'Title or subject'**
  String get titleOrSubjectText;

  /// No description provided for @attachAnonymousDeviceInfo.
  ///
  /// In en, this message translates to:
  /// **'Attach anonymous device info (optional)'**
  String get attachAnonymousDeviceInfo;

  /// No description provided for @doYouWantToReportText.
  ///
  /// In en, this message translates to:
  /// **'Do you want to report a problem with this app? Please describe what went wrong as clear as possible and consider attaching a screenshot so our support staff can help you as fast as possible.'**
  String get doYouWantToReportText;

  /// No description provided for @attachImageText.
  ///
  /// In en, this message translates to:
  /// **'Attach image'**
  String get attachImageText;

  /// No description provided for @attachmentText.
  ///
  /// In en, this message translates to:
  /// **'Screenshot'**
  String get attachmentText;

  /// No description provided for @appVersionLabel.
  ///
  /// In en, this message translates to:
  /// **'App version'**
  String get appVersionLabel;

  /// No description provided for @privacyPolicyLabel.
  ///
  /// In en, this message translates to:
  /// **'Privacy policy'**
  String get privacyPolicyLabel;

  /// No description provided for @disclaimerLabel.
  ///
  /// In en, this message translates to:
  /// **'Disclaimer'**
  String get disclaimerLabel;

  /// No description provided for @generalInformationLabel.
  ///
  /// In en, this message translates to:
  /// **'De Staff employee app provides a quick and easy-to-use platform for both employees and employers to get information on the go.'**
  String get generalInformationLabel;

  /// No description provided for @webAppInformationLabel.
  ///
  /// In en, this message translates to:
  /// **'The mobile app does not contain all the features the web application provides. Therefore, it is an addition to the all-around solution Staff provides for your company.'**
  String get webAppInformationLabel;

  /// No description provided for @betaStateInformationLabel.
  ///
  /// In en, this message translates to:
  /// **'The staff app is currently in beta state. Features are possibly incomplete and are always subject to change in the future.'**
  String get betaStateInformationLabel;

  /// No description provided for @thirdPartySoftwareLabel.
  ///
  /// In en, this message translates to:
  /// **'Third-party software'**
  String get thirdPartySoftwareLabel;

  /// No description provided for @feedbackSuccessMessage.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback!'**
  String get feedbackSuccessMessage;

  /// No description provided for @feedbackErrorMessage.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong, please try again later'**
  String get feedbackErrorMessage;

  /// No description provided for @titleText.
  ///
  /// In en, this message translates to:
  /// **'Third-party software'**
  String get titleText;

  /// No description provided for @generalInformationText.
  ///
  /// In en, this message translates to:
  /// **'The staff app uses third-party software that requires us to show licenses. You can find these licenses below.'**
  String get generalInformationText;

  /// No description provided for @attachPersonalInfoLabel.
  ///
  /// In en, this message translates to:
  /// **'Attach personal information so Staff can contact me if necessary. (optional)'**
  String get attachPersonalInfoLabel;

  /// No description provided for @sendFeedbackButtonLabel.
  ///
  /// In en, this message translates to:
  /// **'Send feedback'**
  String get sendFeedbackButtonLabel;

  /// No description provided for @sendButtonLabel.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get sendButtonLabel;

  /// No description provided for @licenseText.
  ///
  /// In en, this message translates to:
  /// **'The MIT License (MIT)\n\nCopyright (c) 2023\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.'**
  String get licenseText;

  /// No description provided for @schedule_screen_text.
  ///
  /// In en, this message translates to:
  /// **'------> schedule screen text  <------'**
  String get schedule_screen_text;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'Day'**
  String get day;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'Week'**
  String get week;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'Month'**
  String get month;

  /// No description provided for @calendar.
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// No description provided for @downloadSchedule.
  ///
  /// In en, this message translates to:
  /// **'Downloading schedule for '**
  String get downloadSchedule;

  /// No description provided for @departmentSchedule.
  ///
  /// In en, this message translates to:
  /// **'Department schedule'**
  String get departmentSchedule;

  /// No description provided for @noShifts.
  ///
  /// In en, this message translates to:
  /// **'No shifts'**
  String get noShifts;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @startScheduleTime.
  ///
  /// In en, this message translates to:
  /// **'Start time'**
  String get startScheduleTime;

  /// No description provided for @breakScheduleText.
  ///
  /// In en, this message translates to:
  /// **'Break'**
  String get breakScheduleText;

  /// No description provided for @scheduleShift.
  ///
  /// In en, this message translates to:
  /// **'Shifts'**
  String get scheduleShift;

  /// No description provided for @dayRemark.
  ///
  /// In en, this message translates to:
  /// **'Day remark'**
  String get dayRemark;

  /// No description provided for @remarkFromCollege.
  ///
  /// In en, this message translates to:
  /// **'Comment from colleague'**
  String get remarkFromCollege;

  /// No description provided for @remarkFromManager.
  ///
  /// In en, this message translates to:
  /// **'Manager\'s note'**
  String get remarkFromManager;

  /// No description provided for @assignment.
  ///
  /// In en, this message translates to:
  /// **'Assignment'**
  String get assignment;

  /// No description provided for @rosterGroup.
  ///
  /// In en, this message translates to:
  /// **'Roster group'**
  String get rosterGroup;

  /// No description provided for @costCenter.
  ///
  /// In en, this message translates to:
  /// **'Cost centers'**
  String get costCenter;

  /// No description provided for @service.
  ///
  /// In en, this message translates to:
  /// **'Service'**
  String get service;

  /// No description provided for @noDepartment.
  ///
  /// In en, this message translates to:
  /// **'No department'**
  String get noDepartment;

  /// No description provided for @noScheduleAvailable.
  ///
  /// In en, this message translates to:
  /// **'No schedule available'**
  String get noScheduleAvailable;

  /// No description provided for @reflectChanges.
  ///
  /// In en, this message translates to:
  /// **'Swipe down on the dashboard to reflect changes'**
  String get reflectChanges;

  /// No description provided for @noThanks.
  ///
  /// In en, this message translates to:
  /// **'NO THANKS'**
  String get noThanks;

  /// No description provided for @setPin.
  ///
  /// In en, this message translates to:
  /// **'SET PIN'**
  String get setPin;

  /// No description provided for @setPinCode.
  ///
  /// In en, this message translates to:
  /// **'Set PIN code'**
  String get setPinCode;

  /// No description provided for @setPinMsg.
  ///
  /// In en, this message translates to:
  /// **'To protect your data, we recommend that you set a PIN code.\n\nYou can always set a pin later in the settings.'**
  String get setPinMsg;

  /// No description provided for @openFile.
  ///
  /// In en, this message translates to:
  /// **'Open file...'**
  String get openFile;

  /// No description provided for @thankFeedBack.
  ///
  /// In en, this message translates to:
  /// **'Thank you for your feedback!'**
  String get thankFeedBack;

  /// No description provided for @thankFeedBackError.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong please try again later'**
  String get thankFeedBackError;

  /// No description provided for @subscribe.
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get subscribe;

  /// No description provided for @unsubscribe.
  ///
  /// In en, this message translates to:
  /// **'Unsubscribe'**
  String get unsubscribe;

  /// No description provided for @subscribeSuccess.
  ///
  /// In en, this message translates to:
  /// **'Subscribing successful'**
  String get subscribeSuccess;

  /// No description provided for @unsubscribeSuccess.
  ///
  /// In en, this message translates to:
  /// **'Unsubscribing successful'**
  String get unsubscribeSuccess;

  /// No description provided for @swap_text.
  ///
  /// In en, this message translates to:
  /// **'------> swap text  <------'**
  String get swap_text;

  /// No description provided for @openShift.
  ///
  /// In en, this message translates to:
  /// **'Open shifts'**
  String get openShift;

  /// No description provided for @swapShift.
  ///
  /// In en, this message translates to:
  /// **'Swap shift'**
  String get swapShift;

  /// No description provided for @cancelSwap.
  ///
  /// In en, this message translates to:
  /// **'Cancel swap'**
  String get cancelSwap;

  /// No description provided for @requestToSwap.
  ///
  /// In en, this message translates to:
  /// **'Request to swap has been submitted'**
  String get requestToSwap;

  /// No description provided for @swapWithdrawn.
  ///
  /// In en, this message translates to:
  /// **'Swap request withdrawn'**
  String get swapWithdrawn;

  /// No description provided for @refuse.
  ///
  /// In en, this message translates to:
  /// **'Refuse'**
  String get refuse;

  /// No description provided for @registerShift.
  ///
  /// In en, this message translates to:
  /// **'Register for shift'**
  String get registerShift;

  /// No description provided for @managerRemark.
  ///
  /// In en, this message translates to:
  /// **'Manager\'s remark'**
  String get managerRemark;

  /// No description provided for @registeredOnShift.
  ///
  /// In en, this message translates to:
  /// **'You are registered on this shift'**
  String get registeredOnShift;

  /// No description provided for @swapRequestRefused.
  ///
  /// In en, this message translates to:
  /// **'Swap request refused'**
  String get swapRequestRefused;

  /// No description provided for @youHaveRegistered.
  ///
  /// In en, this message translates to:
  /// **'You have registered for this shift'**
  String get youHaveRegistered;

  /// No description provided for @youAreAboutSwapShift.
  ///
  /// In en, this message translates to:
  /// **'You\'re about to swap this shift'**
  String get youAreAboutSwapShift;

  /// No description provided for @noteForColleagues.
  ///
  /// In en, this message translates to:
  /// **'Note for your colleagues'**
  String get noteForColleagues;

  /// No description provided for @noCancel.
  ///
  /// In en, this message translates to:
  /// **'No, cancel'**
  String get noCancel;

  /// No description provided for @yesSwapShift.
  ///
  /// In en, this message translates to:
  /// **'Yes, swap shift'**
  String get yesSwapShift;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @january.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get january;

  /// No description provided for @february.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get february;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @august.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get august;

  /// No description provided for @september.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get september;

  /// No description provided for @october.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get october;

  /// No description provided for @november.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get november;

  /// No description provided for @december.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get december;

  /// No description provided for @leveRequest.
  ///
  /// In en, this message translates to:
  /// **'Leave Request'**
  String get leveRequest;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @end.
  ///
  /// In en, this message translates to:
  /// **'End'**
  String get end;

  /// No description provided for @submittedOn.
  ///
  /// In en, this message translates to:
  /// **'Submitted on'**
  String get submittedOn;

  /// No description provided for @leaveType.
  ///
  /// In en, this message translates to:
  /// **'Leave type'**
  String get leaveType;

  /// No description provided for @state.
  ///
  /// In en, this message translates to:
  /// **'State'**
  String get state;

  /// No description provided for @requestedHours.
  ///
  /// In en, this message translates to:
  /// **'Requested hours'**
  String get requestedHours;

  /// No description provided for @registeredHours.
  ///
  /// In en, this message translates to:
  /// **'Registered hours'**
  String get registeredHours;

  /// No description provided for @reason.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get reason;

  /// No description provided for @declaration.
  ///
  /// In en, this message translates to:
  /// **'Declaration'**
  String get declaration;

  /// No description provided for @declarationsNotFound.
  ///
  /// In en, this message translates to:
  /// **'Declarations not found!'**
  String get declarationsNotFound;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @kilometers.
  ///
  /// In en, this message translates to:
  /// **'Kilometers'**
  String get kilometers;

  /// No description provided for @bon.
  ///
  /// In en, this message translates to:
  /// **'receipt'**
  String get bon;

  /// No description provided for @submitVoucher.
  ///
  /// In en, this message translates to:
  /// **'New declaration'**
  String get submitVoucher;

  /// No description provided for @takePhoto.
  ///
  /// In en, this message translates to:
  /// **'Take Photo'**
  String get takePhoto;

  /// No description provided for @browsingPhone.
  ///
  /// In en, this message translates to:
  /// **'Select from gallery'**
  String get browsingPhone;

  /// No description provided for @mileageRegistration.
  ///
  /// In en, this message translates to:
  /// **'New mileage registration'**
  String get mileageRegistration;

  /// No description provided for @newReceipt.
  ///
  /// In en, this message translates to:
  /// **'New receipt'**
  String get newReceipt;

  /// No description provided for @nameBean.
  ///
  /// In en, this message translates to:
  /// **'Name of the bean'**
  String get nameBean;

  /// No description provided for @receiptSummary.
  ///
  /// In en, this message translates to:
  /// **'Receipt summary'**
  String get receiptSummary;

  /// No description provided for @totalAmount.
  ///
  /// In en, this message translates to:
  /// **'Total amount'**
  String get totalAmount;

  /// No description provided for @receiptDate.
  ///
  /// In en, this message translates to:
  /// **'Receipt date'**
  String get receiptDate;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @saveReceipt.
  ///
  /// In en, this message translates to:
  /// **'Save receipt'**
  String get saveReceipt;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @confirmDeclaration.
  ///
  /// In en, this message translates to:
  /// **'Confirm declaration'**
  String get confirmDeclaration;

  /// No description provided for @notundoneDeclration.
  ///
  /// In en, this message translates to:
  /// **'Once you have submitted this declaration, it cannot be undone.'**
  String get notundoneDeclration;

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Go back'**
  String get goBack;

  /// No description provided for @declrationSave.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get declrationSave;

  /// No description provided for @voucherSucessFully.
  ///
  /// In en, this message translates to:
  /// **'Voucher successfully saved'**
  String get voucherSucessFully;

  /// No description provided for @numberofKiometers.
  ///
  /// In en, this message translates to:
  /// **'Number of kilometers'**
  String get numberofKiometers;

  /// No description provided for @cost.
  ///
  /// In en, this message translates to:
  /// **'Costs'**
  String get cost;

  /// No description provided for @dateVoucher.
  ///
  /// In en, this message translates to:
  /// **'Date voucher'**
  String get dateVoucher;

  /// No description provided for @saveRegistration.
  ///
  /// In en, this message translates to:
  /// **'Submit declaration'**
  String get saveRegistration;

  /// No description provided for @enterKilometers.
  ///
  /// In en, this message translates to:
  /// **'Please enter kilometers'**
  String get enterKilometers;

  /// No description provided for @enterTotalAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter total amount'**
  String get enterTotalAmount;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Please select Date'**
  String get selectDate;

  /// No description provided for @enterDescription.
  ///
  /// In en, this message translates to:
  /// **'Please enter description'**
  String get enterDescription;

  /// No description provided for @ipAuthoriseError.
  ///
  /// In en, this message translates to:
  /// **'Login is not allowed from this device'**
  String get ipAuthoriseError;

  /// No description provided for @enterNameofBon.
  ///
  /// In en, this message translates to:
  /// **'Please enter name of the Bon'**
  String get enterNameofBon;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'nl'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'nl':
      return AppLocalizationsNl();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
