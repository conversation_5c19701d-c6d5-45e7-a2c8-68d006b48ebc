PODS:
  - audio_session (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - open_file (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - open_file (from `.symlinks/plugins/open_file/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FMDB
    - MTBBarcodeScanner

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  Flutter:
    :path: Flutter
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  open_file:
    :path: ".symlinks/plugins/open_file/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audio_session: f08db0697111ac84ba46191b55488c0563bb29c6
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_native_splash: 9e672d3818957718ee006a491730c09deeecace9
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_picker_ios: afb77645f1e1060a27edb6793996ff9b42256909
  just_audio: 6c031bb61297cf218b4462be616638e81c058e97
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  open_file: 898f23092cb034fda4ae3b268d782fe25d525e6b
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 2a68637f8a62df7f6e790a428d1bdf72cb4e2d59
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  shared_preferences_foundation: 4e65c567e7877037d328829a522222c938bf308c
  sqflite: 5b24d06a453c198c13b305ceea4b4286cc07cfe4
  url_launcher_ios: b7f13d9491e07f5230bf3e056ab68678f896980a
  video_player_avfoundation: 5685dd1957b7437a39376959f3502aac60b47aa5
  wakelock_plus: 8c239121a007daa1d6759c6acdc507860273dd2f
  webview_flutter_wkwebview: daa94b5ed120e19439eb7f797649768d6360ebdd

PODFILE CHECKSUM: 47f9449f632f13541a1f0544eed55807dc5eaccf

COCOAPODS: 1.16.2
